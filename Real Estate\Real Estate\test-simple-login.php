<?php
echo "🔐 Testing Simple Admin Login...\n\n";

// Test direct SQLite login (development environment)
function testDirectLogin($email, $password) {
    echo "Testing login for: $email\n";
    
    try {
        // Direct SQLite connection
        $db = new PDO('sqlite:prisma/dev.db');
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Find user by email
        $stmt = $db->prepare('SELECT id, name, email, password, role, isActive FROM User WHERE email = :email');
        $stmt->execute(['email' => $email]);
        $user = $stmt->fetch();

        if (!$user) {
            echo "   ❌ User not found\n";
            return false;
        }

        if (!$user['isActive']) {
            echo "   ❌ Account is deactivated\n";
            return false;
        }
        
        // Verify password
        if (!password_verify($password, $user['password'])) {
            echo "   ❌ Invalid password\n";
            return false;
        }
        
        // Check admin role
        if ($user['role'] !== 'ADMIN') {
            echo "   ❌ Not an admin user (Role: " . $user['role'] . ")\n";
            return false;
        }
        
        echo "   ✅ Login successful!\n";
        echo "   📋 User: " . $user['name'] . " (" . $user['role'] . ")\n";
        
        // Test session creation
        $session_token = bin2hex(random_bytes(32));
        $expires_at = date('Y-m-d H:i:s', strtotime('+30 days'));
        
        $session_stmt = $db->prepare("INSERT INTO UserSession (id, userId, sessionToken, expiresAt) VALUES (?, ?, ?, ?)");
        $session_id = uniqid('sess_', true);
        $session_stmt->execute([$session_id, $user['id'], $session_token, $expires_at]);
        
        echo "   ✅ Session created: " . substr($session_token, 0, 16) . "...\n";
        
        return true;
        
    } catch (Exception $e) {
        echo "   ❌ Error: " . $e->getMessage() . "\n";
        return false;
    }
}

// Test both admin accounts
echo "🧪 Testing Admin Credentials:\n\n";

$credentials = [
    ['email' => '<EMAIL>', 'password' => 'Admin@2024!'],
    ['email' => '<EMAIL>', 'password' => 'new1234']
];

$allPassed = true;
foreach ($credentials as $cred) {
    $result = testDirectLogin($cred['email'], $cred['password']);
    if (!$result) {
        $allPassed = false;
    }
    echo "\n";
}

if ($allPassed) {
    echo "🎉 All admin login tests PASSED!\n\n";
    echo "📋 Working Admin Credentials:\n";
    echo "1. Email: <EMAIL>\n";
    echo "   Password: Admin@2024!\n\n";
    echo "2. Email: <EMAIL>\n";
    echo "   Password: new1234\n\n";
    echo "🌐 Admin Login URL: http://localhost:3000/admin/login\n";
} else {
    echo "❌ Some admin login tests FAILED!\n";
}
?>
