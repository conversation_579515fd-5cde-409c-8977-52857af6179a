<?php
echo "🧪 Testing Admin Login...\n\n";

try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "✅ Connected to SQLite database\n\n";

    // Test credentials
    $testCredentials = [
        ['email' => '<EMAIL>', 'password' => 'Admin@2024!'],
        ['email' => '<EMAIL>', 'password' => 'new1234']
    ];

    foreach ($testCredentials as $index => $creds) {
        echo "🔐 Testing login for: " . $creds['email'] . "\n";

        // Find user by email
        $stmt = $db->prepare('SELECT id, name, email, password, role, isActive FROM User WHERE email = :email');
        $stmt->execute(['email' => $creds['email']]);
        $user = $stmt->fetch();

        if (!$user) {
            echo "❌ User not found\n\n";
            continue;
        }

        echo "   User found: " . $user['name'] . " (Role: " . $user['role'] . ")\n";
        echo "   Active: " . ($user['isActive'] ? 'Yes' : 'No') . "\n";

        // Test password verification
        if (password_verify($creds['password'], $user['password'])) {
            echo "   ✅ Password verification: SUCCESS\n";

            // Check if user is admin
            if ($user['role'] === 'ADMIN') {
                echo "   ✅ Admin role: CONFIRMED\n";
                echo "   🎉 Login test: PASSED\n";
            } else {
                echo "   ❌ Admin role: FAILED (Role: " . $user['role'] . ")\n";
            }
        } else {
            echo "   ❌ Password verification: FAILED\n";
        }

        echo "\n";
    }

    // Test UserSession table exists
    echo "🗄️ Checking UserSession table...\n";
    $tables = $db->query("SELECT name FROM sqlite_master WHERE type='table' AND name='UserSession'")->fetchAll();
    if (!empty($tables)) {
        echo "✅ UserSession table exists\n";

        // Check table structure
        $columns = $db->query("PRAGMA table_info(UserSession)")->fetchAll();
        echo "   Columns: ";
        foreach ($columns as $column) {
            echo $column['name'] . " ";
        }
        echo "\n";
    } else {
        echo "❌ UserSession table missing\n";
    }

} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}
?>
