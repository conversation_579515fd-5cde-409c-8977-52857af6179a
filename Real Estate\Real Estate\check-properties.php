<?php
echo "🏠 Checking Properties Database...\n\n";

try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to SQLite database\n\n";
    
    // Check if properties table exists
    $tables = $db->query("SELECT name FROM sqlite_master WHERE type='table' AND name='Property'")->fetchAll();
    if (empty($tables)) {
        echo "❌ Property table doesn't exist in SQLite\n";
        echo "   This might be why property pages are showing 404\n\n";
        
        // Check for properties table (lowercase)
        $tables2 = $db->query("SELECT name FROM sqlite_master WHERE type='table' AND name='properties'")->fetchAll();
        if (empty($tables2)) {
            echo "❌ properties table (lowercase) also doesn't exist\n";
            echo "   Need to create properties table or run migrations\n";
        } else {
            echo "✅ Found 'properties' table (lowercase)\n";
            testPropertiesTable($db, 'properties');
        }
    } else {
        echo "✅ Property table exists\n";
        testPropertiesTable($db, 'Property');
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

function testPropertiesTable($db, $tableName) {
    echo "\n📋 Testing $tableName table:\n";
    
    // Get table structure
    $columns = $db->query("PRAGMA table_info($tableName)")->fetchAll();
    echo "   Columns: ";
    foreach ($columns as $column) {
        echo $column['name'] . " ";
    }
    echo "\n";
    
    // Count properties
    $count = $db->query("SELECT COUNT(*) as count FROM $tableName")->fetch();
    echo "   Total properties: " . $count['count'] . "\n";
    
    if ($count['count'] > 0) {
        // Get sample properties
        $properties = $db->query("SELECT id, title, approval_status, is_approved FROM $tableName LIMIT 5")->fetchAll();
        echo "   Sample properties:\n";
        foreach ($properties as $prop) {
            $status = isset($prop['approval_status']) ? $prop['approval_status'] : 'N/A';
            $approved = isset($prop['is_approved']) ? ($prop['is_approved'] ? 'Yes' : 'No') : 'N/A';
            echo "   - ID: " . $prop['id'] . " | Title: " . substr($prop['title'], 0, 30) . "... | Status: $status | Approved: $approved\n";
        }
        
        // Check approved properties
        $approvedQuery = "SELECT COUNT(*) as count FROM $tableName WHERE approval_status = 'APPROVED' OR is_approved = 1";
        $approved = $db->query($approvedQuery)->fetch();
        echo "   Approved properties: " . $approved['count'] . "\n";
    } else {
        echo "   ⚠️  No properties found in database\n";
        echo "   This explains why property listings are empty\n";
    }
}

echo "\n🔍 Checking API compatibility:\n";

// Test if the API expects 'properties' or 'Property' table
echo "The PHP API expects 'properties' table (lowercase)\n";
echo "The Prisma schema uses 'Property' table (capitalized)\n";
echo "This mismatch might be causing the 404 errors\n\n";

echo "🔧 Potential Solutions:\n";
echo "1. Update PHP API to use 'Property' table name\n";
echo "2. Create sample properties in the database\n";
echo "3. Fix table name consistency between Prisma and PHP API\n";
?>
