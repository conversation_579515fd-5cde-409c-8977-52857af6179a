(()=>{var e={};e.id=733,e.ids=[733],e.modules={583:(e,t,s)=>{Promise.resolve().then(s.bind(s,6953))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},2031:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Music\\\\Real Estate\\\\Real Estate\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\users\\page.tsx","default")},2744:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3631:(e,t,s)=>{Promise.resolve().then(s.bind(s,2031))},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l,metadata:()=>n});var r=s(7413),a=s(5091),i=s.n(a);s(1135);let n={title:{default:"Real Estate India - Buy, Sell, and Rent Properties",template:"%s | Real Estate India"},description:"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities, or list your property with us. Expert guidance for all your property needs.",keywords:["real estate India","property for sale","property for rent","buy property","sell property","apartments","houses","villas","commercial property"],authors:[{name:"Real Estate India"}],creator:"Real Estate India",publisher:"Real Estate India",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("https://realestate-india.com"),alternates:{canonical:"/"},openGraph:{type:"website",locale:"en_IN",url:"https://realestate-india.com",title:"Real Estate India - Buy, Sell, and Rent Properties",description:"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities.",siteName:"Real Estate India"},twitter:{card:"summary_large_image",title:"Real Estate India - Buy, Sell, and Rent Properties",description:"Find your dream home in India with our comprehensive real estate platform.",creator:"@realestateindia"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function l({children:e}){return(0,r.jsx)("html",{lang:"en",className:"scroll-smooth",children:(0,r.jsx)("body",{className:`${i().variable} font-sans bg-background text-text-primary antialiased`,children:e})})}},6189:(e,t,s)=>{"use strict";var r=s(5773);s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},6953:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(687),a=s(3210),i=s(6189);function n(){let[e,t]=(0,a.useState)(!0),[s,n]=(0,a.useState)([]),[l,o]=(0,a.useState)(""),[d,c]=(0,a.useState)("all"),p=(0,i.useRouter)(),m=async()=>{try{let e=await fetch("/php-backend/api/admin/users.php",{credentials:"include"}),t=await e.json();t.success&&n(t.users||[])}catch(e){console.error("Failed to load users:",e)}finally{t(!1)}},u=async(e,t)=>{try{let s=await fetch("/php-backend/api/admin/toggle-user-status.php",{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({user_id:e,is_active:!t})}),r=await s.json();r.success?(alert(`User ${!t?"activated":"deactivated"} successfully!`),m()):alert("Failed to update user status: "+r.message)}catch(e){alert("Error updating user status")}},x=async(e,t)=>{if(confirm(`Are you sure you want to delete user "${t}"? This action cannot be undone.`))try{let t=await fetch("/php-backend/api/admin/delete-user.php",{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({user_id:e})}),s=await t.json();s.success?(alert("User deleted successfully!"),m()):alert("Failed to delete user: "+s.message)}catch(e){alert("Error deleting user")}},h=s.filter(e=>{let t=e.name?.toLowerCase().includes(l.toLowerCase())||e.email?.toLowerCase().includes(l.toLowerCase()),s="all"===d||e.role===d.toUpperCase();return t&&s});return e?(0,r.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading users..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,r.jsx)("header",{className:"bg-white shadow",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Manage Users"}),(0,r.jsx)("p",{className:"text-gray-600",children:"View and manage all user accounts"})]}),(0,r.jsx)("div",{className:"flex space-x-4",children:(0,r.jsx)("button",{onClick:()=>p.push("/admin/dashboard"),className:"bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700",children:"Back to Dashboard"})})]})})}),(0,r.jsx)("main",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search Users"}),(0,r.jsx)("input",{type:"text",value:l,onChange:e=>o(e.target.value),placeholder:"Search by name or email...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Filter by Role"}),(0,r.jsxs)("select",{value:d,onChange:e=>c(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"All Users"}),(0,r.jsx)("option",{value:"user",children:"Regular Users"}),(0,r.jsx)("option",{value:"admin",children:"Administrators"})]})]})]}),(0,r.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:["Showing ",h.length," of ",s.length," users"]})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Properties"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Joined"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:h.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3",children:(0,r.jsx)("span",{className:"text-white font-semibold",children:e.name?.charAt(0)?.toUpperCase()||"U"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name||"Unknown"}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${"ADMIN"===e.role?"bg-purple-100 text-purple-800":"bg-blue-100 text-blue-800"}`,children:e.role})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:["Total: ",e.property_count||0]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Approved: ",e.approved_properties||0," | Pending: ",e.pending_properties||0]})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${e.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.is_active?"Active":"Inactive"})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString()}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:["ADMIN"!==e.role&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:()=>u(e.id,e.is_active),className:`px-3 py-1 rounded text-xs ${e.is_active?"bg-red-600 text-white hover:bg-red-700":"bg-green-600 text-white hover:bg-green-700"}`,children:e.is_active?"Deactivate":"Activate"}),(0,r.jsx)("button",{onClick:()=>x(e.id,e.name),className:"bg-red-600 text-white px-3 py-1 rounded text-xs hover:bg-red-700",children:"Delete"})]}),"ADMIN"===e.role&&(0,r.jsx)("span",{className:"text-gray-400 text-xs",children:"Protected"})]})})]},e.id))})]})})}),0===h.length&&(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-gray-500",children:"No users found matching your criteria."})})]})})]})}},8031:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},8279:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9592:()=>{},9833:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(5239),a=s(8088),i=s(8170),n=s.n(i),l=s(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["admin",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,2031)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\users\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\admin\\users\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/users/page",pathname:"/admin/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[771],()=>s(9833));module.exports=r})();