<?php
require_once '../../config/database.php';

setCorsHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendError('Method not allowed', 405);
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $property_id = $_GET['id'] ?? '';
    
    if (!$property_id) {
        sendError('Property ID is required', 400);
    }
    
    // Detect database type and use appropriate table names
    $isSQLite = strpos($db->getAttribute(PDO::ATTR_DRIVER_NAME), 'sqlite') !== false;

    if ($isSQLite) {
        // SQLite database (development) - use Prisma table names
        $propertiesTable = 'Property';
        $usersTable = 'User';
        $inquiriesTable = 'Inquiry';
    } else {
        // MySQL database (production) - use lowercase table names
        $propertiesTable = 'properties';
        $usersTable = 'users';
        $inquiriesTable = 'inquiries';
    }

    // Get property with owner information
    $query = "SELECT p.*, u.name as owner_name, u.email as owner_email,
              COALESCE((SELECT COUNT(*) FROM $inquiriesTable WHERE propertyId = p.id), 0) as inquiries_count,
              0 as saved_count
              FROM $propertiesTable p
              LEFT JOIN $usersTable u ON p.ownerId = u.id
              WHERE p.id = :id";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $property_id);
    $stmt->execute();
    
    $property = $stmt->fetch();
    
    if (!$property) {
        sendError('Property not found', 404);
    }
    
    // Only show approved properties to non-admin users
    $user = getCurrentUser($db);
    $isAdmin = $user && $user['role'] === 'ADMIN';
    $isOwner = $user && $user['id'] === $property['ownerId'];

    // Check approval status (handle both Prisma and MySQL field names)
    $approvalStatus = $property['approvalStatus'] ?? $property['approval_status'] ?? 'PENDING';
    $isApproved = $property['isApproved'] ?? $property['is_approved'] ?? false;

    if (!$isAdmin && !$isOwner && $approvalStatus !== 'APPROVED' && !$isApproved) {
        sendError('Property not found', 404);
    }
    
    // Parse JSON fields
    if ($property['images']) {
        $property['images'] = json_decode($property['images'], true) ?: [];
    } else {
        $property['images'] = [];
    }
    
    if ($property['amenities']) {
        $property['amenities'] = json_decode($property['amenities'], true) ?: [];
    } else {
        $property['amenities'] = [];
    }
    
    // Convert boolean fields (handle both Prisma and MySQL field names)
    $property['is_featured'] = (bool)($property['isFeatured'] ?? $property['is_featured'] ?? false);
    $property['is_approved'] = (bool)($property['isApproved'] ?? $property['is_approved'] ?? false);
    $property['is_active'] = (bool)($property['isActive'] ?? $property['is_active'] ?? true);
    
    // Convert numeric fields
    $property['price'] = (int)$property['price'];
    $property['bedrooms'] = (int)$property['bedrooms'];
    $property['bathrooms'] = (int)$property['bathrooms'];
    $property['area'] = (int)$property['area'];
    $property['view_count'] = (int)$property['view_count'];
    $property['inquiries_count'] = (int)$property['inquiries_count'];
    $property['saved_count'] = (int)$property['saved_count'];
    
    // Increment view count (only for approved properties)
    if ($approvalStatus === 'APPROVED' || $isApproved) {
        $viewCountField = $isSQLite ? 'viewCount' : 'view_count';
        $update_query = "UPDATE $propertiesTable SET $viewCountField = $viewCountField + 1 WHERE id = :id";
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':id', $property_id);
        $update_stmt->execute();
        $property['view_count']++;
    }
    
    sendResponse([
        'success' => true,
        'property' => $property
    ]);
    
} catch (Exception $e) {
    error_log("Get property error: " . $e->getMessage());
    sendError('Failed to get property', 500);
}

// Helper function to get current user
function getCurrentUser($db) {
    // Check for session token in cookies
    if (!isset($_COOKIE['session_token'])) {
        return null;
    }
    
    $session_token = $_COOKIE['session_token'];
    
    $query = "SELECT u.* FROM users u 
              JOIN user_sessions s ON u.id = s.user_id 
              WHERE s.session_token = :token AND s.expires_at > NOW()";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':token', $session_token);
    $stmt->execute();
    
    return $stmt->fetch();
}
?>
