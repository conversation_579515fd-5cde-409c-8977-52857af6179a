<?php
echo "🧪 Testing Properties (Simple)...\n\n";

try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to SQLite database\n\n";
    
    // Test 1: Count properties
    echo "1. Testing Property Count:\n";
    $count = $db->query('SELECT COUNT(*) as count FROM Property')->fetch();
    echo "   📊 Total properties: " . $count['count'] . "\n\n";
    
    // Test 2: List properties
    echo "2. Testing Property Listing:\n";
    $properties = $db->query('SELECT id, title, price, city, state, images FROM Property LIMIT 5')->fetchAll();
    
    foreach ($properties as $property) {
        echo "   🏠 " . $property['title'] . "\n";
        echo "      💰 Price: ₹" . number_format($property['price']) . "\n";
        echo "      📍 Location: " . $property['city'] . ", " . $property['state'] . "\n";
        echo "      🆔 ID: " . $property['id'] . "\n";
        
        // Test images
        $images = json_decode($property['images'], true);
        if ($images && count($images) > 0) {
            echo "      🖼️  First image: " . $images[0] . "\n";
        } else {
            echo "      ❌ No images\n";
        }
        echo "\n";
    }
    
    // Test 3: Property with owner info
    echo "3. Testing Property with Owner Info:\n";
    $query = 'SELECT p.*, u.name as owner_name, u.email as owner_email 
              FROM Property p 
              LEFT JOIN User u ON p.ownerId = u.id 
              LIMIT 1';
    
    $propertyWithOwner = $db->query($query)->fetch();
    
    if ($propertyWithOwner) {
        echo "   ✅ Property with owner found:\n";
        echo "      🏠 Property: " . $propertyWithOwner['title'] . "\n";
        echo "      👤 Owner: " . $propertyWithOwner['owner_name'] . "\n";
        echo "      📧 Email: " . $propertyWithOwner['owner_email'] . "\n";
    } else {
        echo "   ❌ No property with owner found\n";
    }
    
    echo "\n🎯 API Simulation Test:\n";
    
    // Simulate the API response format
    $apiProperties = [];
    foreach ($properties as $property) {
        $apiProperty = [
            'id' => $property['id'],
            'title' => $property['title'],
            'price' => (int)$property['price'],
            'city' => $property['city'],
            'state' => $property['state'],
            'images' => json_decode($property['images'], true) ?: [],
        ];
        $apiProperties[] = $apiProperty;
    }
    
    $apiResponse = [
        'properties' => $apiProperties,
        'pagination' => [
            'page' => 1,
            'limit' => 5,
            'total' => $count['count'],
            'pages' => ceil($count['count'] / 5)
        ]
    ];
    
    echo "   📋 API Response Structure:\n";
    echo "   - Properties count: " . count($apiResponse['properties']) . "\n";
    echo "   - Total in DB: " . $apiResponse['pagination']['total'] . "\n";
    echo "   - First property ID: " . ($apiResponse['properties'][0]['id'] ?? 'N/A') . "\n";
    
    echo "\n✅ All tests passed! Properties are ready for frontend.\n\n";
    
    echo "🌐 Next Steps:\n";
    echo "1. Start PHP backend server: php -S localhost:8000 -t php-backend\n";
    echo "2. Start Next.js frontend: npm run dev\n";
    echo "3. Visit: http://localhost:3000/properties\n";
    echo "4. Test property details: http://localhost:3000/properties/[property-id]\n";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}
?>
