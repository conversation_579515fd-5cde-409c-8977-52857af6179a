(()=>{var e={};e.id=879,e.ids=[879],e.modules={713:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=s(5239),a=s(8088),o=s(8170),n=s.n(o),i=s(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(r,l);let d={children:["",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8255)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\signup\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\signup\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/signup/page",pathname:"/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2002:(e,r,s)=>{Promise.resolve().then(s.bind(s,8255))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3221:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>u});var t=s(687),a=s(3210),o=s(6189),n=s(5814),i=s.n(n),l=s(9190),d=s(1317),c=s(216);function u(){let[e,r]=(0,a.useState)({name:"",email:"",password:"",confirmPassword:"",phone:""}),[s,n]=(0,a.useState)(""),[u,p]=(0,a.useState)(!1),m=(0,o.useRouter)(),x=s=>{r({...e,[s.target.name]:s.target.value})},h=async r=>{if(r.preventDefault(),p(!0),n(""),e.password!==e.confirmPassword){n("Passwords do not match"),p(!1);return}if(e.password.length<6){n("Password must be at least 6 characters long"),p(!1);return}try{let r=await c.R2.signup(e.name,e.email,e.password,e.phone);r.success?m.push("/login?message=Account created successfully"):n(r.error||"An error occurred")}catch(e){console.error("Signup error:",e),n(e.message||"An error occurred. Please try again.")}finally{p(!1)}};return(0,t.jsxs)("main",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(l.Navbar,{}),(0,t.jsx)("div",{className:"flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Create your account"}),(0,t.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,t.jsx)(i(),{href:"/login",className:"font-medium text-primary-600 hover:text-primary-700",children:"sign in to your existing account"})]})]}),(0,t.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:h,children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,t.jsx)("input",{id:"name",name:"name",type:"text",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary sm:text-sm",placeholder:"Enter your full name",value:e.name,onChange:x})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email Address"}),(0,t.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary sm:text-sm",placeholder:"Enter your email address",value:e.email,onChange:x})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Phone Number (Optional)"}),(0,t.jsx)("input",{id:"phone",name:"phone",type:"tel",className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary sm:text-sm",placeholder:"Enter your phone number",value:e.phone,onChange:x})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,t.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary sm:text-sm",placeholder:"Create a password",value:e.password,onChange:x})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),(0,t.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",autoComplete:"new-password",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary sm:text-sm",placeholder:"Confirm your password",value:e.confirmPassword,onChange:x})]})]}),s&&(0,t.jsx)("div",{className:"text-red-600 text-sm text-center",children:s}),(0,t.jsx)("div",{children:(0,t.jsx)("button",{type:"submit",disabled:u,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed",children:u?"Creating account...":"Create account"})})]})]})}),(0,t.jsx)(d.w,{})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},6189:(e,r,s)=>{"use strict";var t=s(5773);s.o(t,"useRouter")&&s.d(r,{useRouter:function(){return t.useRouter}}),s.o(t,"useSearchParams")&&s.d(r,{useSearchParams:function(){return t.useSearchParams}})},8255:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Music\\\\Real Estate\\\\Real Estate\\\\src\\\\app\\\\signup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\signup\\page.tsx","default")},8858:(e,r,s)=>{Promise.resolve().then(s.bind(s,3221))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[771,814,436,317],()=>s(713));module.exports=t})();