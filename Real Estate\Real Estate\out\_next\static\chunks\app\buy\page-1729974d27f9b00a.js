(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[329],{1183:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var r=s(5155),a=s(2115),l=s(5494),i=s(6821),n=s(4527),o=s(4659),c=s(9604),d=s(6874),x=s.n(d),m=s(6766);function h(){let[e,t]=(0,a.useState)({type:"",minPrice:"",maxPrice:"",bedrooms:"",city:""}),[s,d]=(0,a.useState)([]),[h,p]=(0,a.useState)(!1),[u,g]=(0,a.useState)({currentPage:1,totalPages:1,totalItems:0,hasNext:!1,hasPrev:!1}),f=async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;p(!0);try{let e=Object.entries(t).reduce((e,t)=>{let[s,r]=t;return r&&""!==r.trim()&&(e[s]=r),e},{}),r=new URLSearchParams({page:s.toString(),limit:"12",...e}).toString(),a=await fetch("/php-backend/api/properties/search.php?".concat(r));if(!a.ok)throw Error("HTTP error! status: ".concat(a.status));let l=await a.json();d(l.properties),g(l.pagination)}catch(e){console.error("Error loading properties:",e)}finally{p(!1)}};(0,a.useEffect)(()=>{f()},[]);let b=(0,a.useCallback)(e=>{t(e),f(e,1)},[]);return(0,r.jsxs)("main",{className:"min-h-screen",children:[(0,r.jsx)(l.Navbar,{}),(0,r.jsxs)("section",{className:"relative h-[500px] flex items-center justify-center",children:[(0,r.jsx)("div",{className:"absolute inset-0 z-0",children:(0,r.jsx)(m.default,{src:"https://images.unsplash.com/photo-1560518883-ce09059eeffa?q=80&w=1473&auto=format&fit=crop",alt:"Buy Properties",fill:!0,className:"object-cover brightness-50"})}),(0,r.jsxs)("div",{className:"container-custom relative z-10 text-center text-white",children:[(0,r.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-6",children:"Find Your Dream Home"}),(0,r.jsx)("p",{className:"text-xl mb-8 max-w-2xl mx-auto",children:"Discover a wide range of properties for sale that match your lifestyle and budget"}),(0,r.jsx)(n.SearchBar,{})]})]}),(0,r.jsx)("section",{className:"py-16 bg-gray-100",children:(0,r.jsxs)("div",{className:"container-custom",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold mb-12 text-center",children:"Your Home Buying Journey"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4",children:"1"}),(0,r.jsx)("h3",{className:"text-xl font-bold mb-3",children:"Get Pre-Approved"}),(0,r.jsx)("p",{className:"text-text-secondary",children:"Start by getting pre-approved for a mortgage to understand your budget and show sellers you're serious."})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4",children:"2"}),(0,r.jsx)("h3",{className:"text-xl font-bold mb-3",children:"Find Your Home"}),(0,r.jsx)("p",{className:"text-text-secondary",children:"Browse our listings, schedule viewings, and find the perfect property that meets your needs."})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4",children:"3"}),(0,r.jsx)("h3",{className:"text-xl font-bold mb-3",children:"Make an Offer"}),(0,r.jsx)("p",{className:"text-text-secondary",children:"Work with our agents to make a competitive offer and negotiate the best terms for your purchase."})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4",children:"4"}),(0,r.jsx)("h3",{className:"text-xl font-bold mb-3",children:"Close the Deal"}),(0,r.jsx)("p",{className:"text-text-secondary",children:"Complete inspections, secure financing, and sign the final paperwork to become a homeowner."})]})]}),(0,r.jsx)("div",{className:"text-center mt-10",children:(0,r.jsx)(x(),{href:"/buying-guide",className:"btn-primary",children:"View Complete Buying Guide"})})]})}),(0,r.jsx)("section",{className:"py-16",children:(0,r.jsxs)("div",{className:"container-custom",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold mb-12 text-center",children:"Properties For Sale"}),(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,r.jsx)("div",{className:"lg:w-1/4",children:(0,r.jsx)(c.f,{onFilterChange:b})}),(0,r.jsxs)("div",{className:"lg:w-3/4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsxs)("p",{className:"text-text-secondary",children:["Showing ",(0,r.jsx)("span",{className:"font-semibold",children:s.length})," of ",(0,r.jsx)("span",{className:"font-semibold",children:u.totalItems})," properties"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("label",{htmlFor:"sort",className:"text-text-secondary",children:"Sort by:"}),(0,r.jsxs)("select",{id:"sort",className:"border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",children:[(0,r.jsx)("option",{value:"newest",children:"Newest"}),(0,r.jsx)("option",{value:"price-asc",children:"Price (Low to High)"}),(0,r.jsx)("option",{value:"price-desc",children:"Price (High to Low)"}),(0,r.jsx)("option",{value:"popular",children:"Most Popular"})]})]})]}),h?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading properties..."})]}):0===s.length?(0,r.jsxs)("div",{className:"col-span-full text-center py-8",children:[(0,r.jsx)("p",{className:"text-gray-500",children:"No properties found matching your criteria"}),(0,r.jsx)(x(),{href:"/properties",className:"text-primary hover:underline mt-2 inline-block",children:"Browse All Properties"})]}):(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:s.map(e=>(0,r.jsx)(o.y,{property:e},e.id))}),u.totalPages>1&&(0,r.jsx)("div",{className:"mt-12 flex justify-center",children:(0,r.jsxs)("nav",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:()=>u.hasPrev&&f(e,u.currentPage-1),disabled:!u.hasPrev,className:"w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),Array.from({length:Math.min(5,u.totalPages)},(t,s)=>{let a=s+1;return(0,r.jsx)("button",{onClick:()=>f(e,a),className:"w-10 h-10 rounded-md flex items-center justify-center ".concat(u.currentPage===a?"bg-primary text-white":"border border-gray-300 hover:bg-gray-100"),children:a},a)}),(0,r.jsx)("button",{onClick:()=>u.hasNext&&f(e,u.currentPage+1),disabled:!u.hasNext,className:"w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})})]})]})]})}),(0,r.jsx)("section",{className:"py-16 bg-gray-100",children:(0,r.jsxs)("div",{className:"container-custom",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold mb-12 text-center",children:"Home Buying Tips"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,r.jsx)("div",{className:"relative h-48",children:(0,r.jsx)(m.default,{src:"https://images.unsplash.com/photo-1560520031-3a4dc4e9de0c?q=80&w=1473&auto=format&fit=crop",alt:"Financial Planning",fill:!0,className:"object-cover"})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-xl font-bold mb-3",children:"Financial Planning"}),(0,r.jsx)("p",{className:"text-text-secondary mb-4",children:"Learn how to budget for your home purchase, including down payment, closing costs, and ongoing expenses."}),(0,r.jsx)(x(),{href:"/blog/financial-planning",className:"text-primary font-semibold hover:underline",children:"Read More"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,r.jsx)("div",{className:"relative h-48",children:(0,r.jsx)(m.default,{src:"https://images.unsplash.com/photo-1628744448840-55bdb2497bd4?q=80&w=1470&auto=format&fit=crop",alt:"Home Inspection",fill:!0,className:"object-cover"})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-xl font-bold mb-3",children:"Home Inspection Guide"}),(0,r.jsx)("p",{className:"text-text-secondary mb-4",children:"What to look for during a home inspection and red flags that could save you from a costly mistake."}),(0,r.jsx)(x(),{href:"/blog/home-inspection",className:"text-primary font-semibold hover:underline",children:"Read More"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,r.jsx)("div",{className:"relative h-48",children:(0,r.jsx)(m.default,{src:"https://images.unsplash.com/photo-1450101499163-c8848c66ca85?q=80&w=1470&auto=format&fit=crop",alt:"Negotiation Strategies",fill:!0,className:"object-cover"})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-xl font-bold mb-3",children:"Negotiation Strategies"}),(0,r.jsx)("p",{className:"text-text-secondary mb-4",children:"Expert tips on negotiating the best price and terms when making an offer on your dream home."}),(0,r.jsx)(x(),{href:"/blog/negotiation-strategies",className:"text-primary font-semibold hover:underline",children:"Read More"})]})]})]})]})}),(0,r.jsx)("section",{className:"py-16 bg-primary text-white",children:(0,r.jsxs)("div",{className:"container-custom text-center",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold mb-6",children:"Ready to Find Your Dream Home?"}),(0,r.jsx)("p",{className:"text-xl mb-8 max-w-2xl mx-auto",children:"Our expert agents are ready to help you navigate the buying process from start to finish."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row justify-center gap-4",children:[(0,r.jsx)(x(),{href:"/contact",className:"btn-white",children:"Contact an Agent"}),(0,r.jsx)(x(),{href:"/properties",className:"btn-outline-white",children:"Browse All Properties"})]})]})}),(0,r.jsx)(i.w,{})]})}},1469:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return o},getImageProps:function(){return n}});let r=s(8229),a=s(8883),l=s(3063),i=r._(s(1193));function n(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let o=l.Image},4659:(e,t,s)=>{"use strict";s.d(t,{y:()=>n});var r=s(5155),a=s(6874),l=s.n(a);function i(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],s=t?"₹":"";if(e>=1e7){let t=e/1e7;return"".concat(s).concat(t.toFixed(+(t%1!=0))," Cr")}if(e>=1e5){let t=e/1e5;return"".concat(s).concat(t.toFixed(+(t%1!=0))," L")}if(!(e>=1e3))return"".concat(s).concat(e.toLocaleString("en-IN"));{let t=e/1e3;return"".concat(s).concat(t.toFixed(+(t%1!=0)),"K")}}function n(e){var t,s,a,n;let{property:o}=e,c=(e=>{try{return JSON.parse(e||"[]")}catch(e){return[]}})(o.images),d=c.length>0&&(t=c[0])?t.startsWith("http")?t:t.startsWith("/")?"https://housing.okayy.in".concat(t):"https://housing.okayy.in/php-backend/uploads/".concat(t):"https://via.placeholder.com/400x300/e5e7eb/6b7280?text=Property+Image",x=(e=>{let t=new Date(e);return 7>=Math.ceil(Math.abs(new Date().getTime()-t.getTime())/864e5)})(o.createdAt);return(0,r.jsxs)("div",{className:"card-elevated group overflow-hidden animate-fade-in",children:[(0,r.jsxs)("div",{className:"relative h-72 w-full overflow-hidden",children:[(0,r.jsx)("img",{src:d,alt:o.title,className:"w-full h-full object-cover transition-all duration-700 group-hover:scale-110 group-hover:brightness-110",onError:e=>{e.currentTarget.src="https://via.placeholder.com/400x300/e5e7eb/6b7280?text=Property+Image"}}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,r.jsx)("div",{className:"absolute top-4 left-4",children:(0,r.jsx)("span",{className:"px-4 py-2 rounded-xl text-sm font-semibold backdrop-blur-sm border border-white/20 ".concat("PG"===o.type||o.title.toLowerCase().includes("pg")?"bg-purple-500/90 text-white":o.title.toLowerCase().includes("rent")||o.price<1e5?"bg-accent-500/90 text-white":"bg-primary-500/90 text-white"," shadow-soft"),children:"PG"===o.type||o.title.toLowerCase().includes("pg")?"PG":o.title.toLowerCase().includes("rent")||o.price<1e5?"For Rent":"For Sale"})}),x&&(0,r.jsx)("div",{className:"absolute top-4 right-16",children:(0,r.jsx)("span",{className:"px-4 py-2 rounded-xl text-sm font-semibold bg-success-500/90 text-white backdrop-blur-sm border border-white/20 shadow-soft animate-bounce-subtle",children:"✨ New"})}),(0,r.jsx)("div",{className:"absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-300",children:(0,r.jsx)("button",{className:"w-full py-3 bg-white/95 backdrop-blur-sm text-text-primary font-semibold rounded-xl shadow-soft hover:bg-white transition-all duration-300",children:"Quick View"})})]}),(0,r.jsxs)("div",{className:"p-6 space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-text-primary group-hover:text-primary-600 transition-colors duration-300 line-clamp-2",children:o.title}),(0,r.jsx)("p",{className:"text-text-tertiary text-sm flex items-center",children:(0,r.jsxs)("span",{className:"line-clamp-1",children:[o.address,", ",o.city,", ",o.state]})})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("span",{className:"text-2xl font-bold text-gradient",children:(s=o.price,a=o.title,n=o.type,a.toLowerCase().includes("rent")||"PG"===n||a.toLowerCase().includes("pg")||a.toLowerCase().includes("paying guest")||s<1e5?"".concat(i(s),"/month"):i(s))}),(o.title.toLowerCase().includes("rent")||"PG"===o.type||o.title.toLowerCase().includes("pg")||o.price<1e5)&&(0,r.jsx)("p",{className:"text-xs text-text-tertiary",children:"per month"})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-sm text-text-tertiary",children:"Price per sqft"}),(0,r.jsxs)("div",{className:"text-lg font-semibold text-text-secondary",children:["₹",Math.round(o.price/o.area).toLocaleString("en-IN")]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 py-4 border-t border-gray-100",children:[o.bedrooms&&(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-lg font-bold text-blue-600",children:o.bedrooms})}),(0,r.jsx)("div",{className:"text-xs text-text-tertiary",children:"Beds"})]}),o.bathrooms&&(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-green-50 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-lg font-bold text-green-600",children:o.bathrooms})}),(0,r.jsx)("div",{className:"text-xs text-text-tertiary",children:"Baths"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-purple-50 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-lg font-bold text-purple-600",children:o.area})}),(0,r.jsx)("div",{className:"text-xs text-text-tertiary",children:"sqft"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)(l(),{href:"PG"===o.type?"/pg/".concat(o.id):"/properties/".concat(o.id),className:"btn-primary flex-1 text-center",children:"View Details"}),(0,r.jsx)("button",{className:"px-4 py-3 bg-primary-50 text-primary-600 font-semibold rounded-xl hover:bg-primary-100 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",children:"Call"})]})]})]},o.id)}},6766:(e,t,s)=>{"use strict";s.d(t,{default:()=>a.a});var r=s(1469),a=s.n(r)},7548:(e,t,s)=>{Promise.resolve().then(s.bind(s,1183))}},e=>{var t=t=>e(e.s=t);e.O(0,[874,63,494,821,527,604,441,684,358],()=>t(7548)),_N_E=e.O()}]);