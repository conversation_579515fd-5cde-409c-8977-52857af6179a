(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{1409:(e,r,s)=>{Promise.resolve().then(s.bind(s,9690))},5695:(e,r,s)=>{"use strict";var a=s(8999);s.o(a,"useRouter")&&s.d(r,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(r,{useSearchParams:function(){return a.useSearchParams}})},9690:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>u});var a=s(5155),t=s(2115),n=s(5695),o=s(6874),l=s.n(o),i=s(5494),d=s(6821),c=s(1008);function u(){let[e,r]=(0,t.useState)(""),[s,o]=(0,t.useState)(""),[u,m]=(0,t.useState)(""),[p,x]=(0,t.useState)(!1),h=(0,n.useRouter)();(0,t.useEffect)(()=>{let e=new URLSearchParams(window.location.search).get("error");e&&m(decodeURIComponent(e))},[]);let f=async r=>{r.preventDefault(),x(!0),m("");try{let r=await c.R2.login(e,s);r.success?(localStorage.setItem("user",JSON.stringify(r.user)),"ADMIN"===r.user.role?h.push("/admin/dashboard"):h.push("/dashboard")):m("Login failed. Please try again.")}catch(e){console.error("Login error:",e),m(e.message||"Login failed. Please check your credentials.")}finally{x(!1)}};return(0,a.jsxs)("main",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(i.Navbar,{}),(0,a.jsx)("div",{className:"flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),(0,a.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,a.jsx)(l(),{href:"/signup",className:"font-medium text-primary-600 hover:text-primary-700",children:"create a new account"})]})]}),(0,a.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:f,children:[(0,a.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"sr-only",children:"Email address"}),(0,a.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm",placeholder:"Email address",value:e,onChange:e=>r(e.target.value)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"sr-only",children:"Password"}),(0,a.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm",placeholder:"Password",value:s,onChange:e=>o(e.target.value)})]})]}),u&&(0,a.jsx)("div",{className:"text-red-600 text-sm text-center",children:u}),(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsx)("div",{className:"text-sm",children:(0,a.jsx)(l(),{href:"/forgot-password",className:"font-medium text-primary-600 hover:text-primary-700",children:"Forgot your password?"})})}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",disabled:p,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed",children:p?"Signing in...":"Sign in"})})]})]})}),(0,a.jsx)(d.w,{})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[874,494,821,441,684,358],()=>r(1409)),_N_E=e.O()}]);