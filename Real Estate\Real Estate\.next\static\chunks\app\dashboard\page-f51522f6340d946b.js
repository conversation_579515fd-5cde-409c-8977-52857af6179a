(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{4879:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>p});var t=r(5155),a=r(2115),l=r(5695),i=r(5494),o=r(6821),n=r(6874),c=r.n(n),d=r(1008);function p(){let e=(0,l.useRouter)(),[s,r]=(0,a.useState)(null),[n,p]=(0,a.useState)([]),[m,x]=(0,a.useState)([]),[h,g]=(0,a.useState)([]),[u,b]=(0,a.useState)(0),[y,N]=(0,a.useState)("all");return((0,a.useEffect)(()=>{(async()=>{try{let s=await d.R2.checkSession();r(s),s.user||e.push("/login?error=Please%20login%20to%20access%20dashboard")}catch(s){console.error("Session fetch error:",s),e.push("/login?error=Please%20login%20to%20access%20dashboard")}})()},[]),(0,a.useEffect)(()=>{(async()=>{if(!(null==s?void 0:s.user))return console.log("No user session, skipping dashboard data fetch");try{console.log("Fetching dashboard data for user:",s.user.id);let[e,r]=await Promise.all([d.Eo.getProperties(),d.Eo.getInquiries()]);console.log("Properties response:",e),console.log("Inquiries response:",r),e&&e.success&&Array.isArray(e.properties)?p(e.properties):Array.isArray(e)?p(e):(console.error("API /api/user/properties did not return expected format:",e),p([])),r&&r.success&&Array.isArray(r.inquiries)?x(r.inquiries):Array.isArray(r)?x(r):(console.error("API /api/user/inquiries did not return expected format:",r),x([])),g([])}catch(e){console.error("Failed to fetch dashboard data:",e),console.error("Error details:",e instanceof Error?e.message:"Unknown error"),p([]),x([]),g([])}})()},[s,u]),null===s)?(0,t.jsx)("main",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})}):s.user?(0,t.jsxs)("main",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(i.Navbar,{}),(0,t.jsxs)("div",{className:"container-custom py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Welcome to Your Dashboard!"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"Manage your properties, inquiries, and saved listings here."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Total Properties"}),(0,t.jsx)("p",{className:"text-3xl font-bold text-primary",children:n.length}),(0,t.jsx)("p",{className:"text-gray-600",children:"Your listed properties"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Approved Properties"}),(0,t.jsx)("p",{className:"text-3xl font-bold text-green-600",children:n.filter(e=>"APPROVED"===e.approvalStatus).length}),(0,t.jsx)("p",{className:"text-gray-600",children:"Properties approved by admin"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Pending Properties"}),(0,t.jsx)("p",{className:"text-3xl font-bold text-yellow-600",children:n.filter(e=>"PENDING"===e.approvalStatus).length}),(0,t.jsx)("p",{className:"text-gray-600",children:"Properties awaiting approval"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Rejected Properties"}),(0,t.jsx)("p",{className:"text-3xl font-bold text-red-600",children:n.filter(e=>"REJECTED"===e.approvalStatus).length}),(0,t.jsx)("p",{className:"text-gray-600",children:"Properties rejected by admin"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Inquiries Received"}),(0,t.jsx)("p",{className:"text-3xl font-bold text-primary",children:m.length}),(0,t.jsx)("p",{className:"text-gray-600",children:"Inquiries on your properties"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Saved Properties"}),(0,t.jsx)("p",{className:"text-3xl font-bold text-primary",children:h.length}),(0,t.jsx)("p",{className:"text-gray-600",children:"Properties you have saved"})]})]}),(0,t.jsxs)("div",{className:"mt-8 bg-white rounded-lg shadow p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Your Listed Properties"}),(0,t.jsxs)("div",{className:"flex space-x-4 mb-4",children:[(0,t.jsxs)("button",{onClick:()=>N("all"),className:"px-4 py-2 rounded-md text-sm font-medium ".concat("all"===y?"bg-primary text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:["All (",n.length,")"]}),(0,t.jsxs)("button",{onClick:()=>N("approved"),className:"px-4 py-2 rounded-md text-sm font-medium ".concat("approved"===y?"bg-primary text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:["Approved (",n.filter(e=>"APPROVED"===e.approvalStatus).length,")"]}),(0,t.jsxs)("button",{onClick:()=>N("pending"),className:"px-4 py-2 rounded-md text-sm font-medium ".concat("pending"===y?"bg-primary text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:["Pending (",n.filter(e=>"PENDING"===e.approvalStatus).length,")"]}),(0,t.jsxs)("button",{onClick:()=>N("rejected"),className:"px-4 py-2 rounded-md text-sm font-medium ".concat("rejected"===y?"bg-primary text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:["Rejected (",n.filter(e=>"REJECTED"===e.approvalStatus).length,")"]})]}),0===n.filter(e=>"approved"===y?"APPROVED"===e.approvalStatus:"pending"===y?"PENDING"===e.approvalStatus:"rejected"!==y||"REJECTED"===e.approvalStatus).length?(0,t.jsx)("p",{className:"text-gray-600",children:"No properties found for the selected status."}):(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:n.filter(e=>{let s=e.approvalStatus||e.approval_status||"";return"approved"===y?"APPROVED"===s:"pending"===y?"PENDING"===s:"rejected"!==y||"REJECTED"===s}).map(e=>{var s,r;let a=[];try{"string"==typeof e.images&&e.images?a=JSON.parse(e.images):Array.isArray(e.images)&&(a=e.images)}catch(e){console.error("Error parsing property images:",e),a=[]}let l=a&&a.length>0?a[0]:"https://via.placeholder.com/400x300/e5e7eb/6b7280?text=Property+Image";return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,t.jsx)("img",{src:l,alt:e.title||"Property",className:"w-full h-48 object-cover rounded-md mb-4",onError:e=>{e.currentTarget.src="https://via.placeholder.com/400x300/e5e7eb/6b7280?text=Property+Image"}}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-1",children:e.title||"Untitled Property"}),(0,t.jsxs)("p",{className:"text-primary font-bold",children:[e.currency||"INR"," ",(e.price||0).toLocaleString()]}),(0,t.jsxs)("p",{className:"text-gray-600 text-sm",children:[e.type||"Property"," in ",e.city||"Unknown",", ",e.state||"Unknown"]}),(0,t.jsxs)("p",{className:"text-gray-500 text-xs mt-2",children:["Status: ",e.approvalStatus||e.approval_status||"Unknown"]}),(0,t.jsxs)("p",{className:"text-gray-500 text-xs",children:["Views: ",e.viewCount||e.view_count||0]}),(0,t.jsxs)("p",{className:"text-gray-500 text-xs",children:["Inquiries: ",e.inquiries_count||(null==(s=e._count)?void 0:s.inquiries)||0]}),(0,t.jsxs)("p",{className:"text-gray-500 text-xs",children:["Saved By: ",e.saved_count||(null==(r=e._count)?void 0:r.savedBy)||0]}),(0,t.jsx)(c(),{href:"/properties/".concat(e.id),className:"text-blue-500 hover:underline text-sm mt-2 block",children:"View Details"})]},e.id)})})]}),(0,t.jsxs)("div",{className:"mt-8 bg-white rounded-lg shadow p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Quick Actions"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsx)(c(),{href:"/properties/create/",className:"btn-primary text-center",children:"List New Property"}),(0,t.jsx)(c(),{href:"/properties/",className:"btn-secondary text-center",children:"Browse Properties"}),(0,t.jsx)("button",{onClick:()=>b(e=>e+1),className:"btn-secondary text-center",children:"Refresh Dashboard Data"})]})]})]}),(0,t.jsx)(o.w,{})]}):null}},5695:(e,s,r)=>{"use strict";var t=r(8999);r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(s,{useSearchParams:function(){return t.useSearchParams}})},6504:(e,s,r)=>{Promise.resolve().then(r.bind(r,4879))}},e=>{var s=s=>e(e.s=s);e.O(0,[874,494,821,441,684,358],()=>s(6504)),_N_E=e.O()}]);