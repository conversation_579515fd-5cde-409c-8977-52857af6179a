<?php
echo "🧪 Testing Property API Endpoints...\n\n";

// Test the properties API directly
function testPropertiesAPI() {
    echo "1. Testing Properties List API:\n";
    
    try {
        // Include the database configuration
        require_once 'php-backend/config/database.php';
        
        // Simulate the API call
        $_SERVER['REQUEST_METHOD'] = 'GET';
        $_GET['limit'] = '10';
        
        $database = new Database();
        $db = $database->getConnection();
        
        // Call the function directly
        ob_start();
        handleGetProperties($db);
        $output = ob_get_clean();
        
        echo "   API Response: " . substr($output, 0, 200) . "...\n";
        
        // Try to decode JSON
        $data = json_decode($output, true);
        if ($data && isset($data['properties'])) {
            echo "   ✅ API returned " . count($data['properties']) . " properties\n";
            
            if (count($data['properties']) > 0) {
                $firstProperty = $data['properties'][0];
                echo "   📋 First property: " . $firstProperty['title'] . "\n";
                echo "   🏷️  Price: ₹" . number_format($firstProperty['price']) . "\n";
                echo "   📍 Location: " . $firstProperty['city'] . ", " . $firstProperty['state'] . "\n";
            }
        } else {
            echo "   ❌ Invalid API response format\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ API Error: " . $e->getMessage() . "\n";
    }
}

function handleGetProperties($db) {
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 12;
    $offset = ($page - 1) * $limit;
    
    // Detect database type and use appropriate table names
    $isSQLite = strpos($db->getAttribute(PDO::ATTR_DRIVER_NAME), 'sqlite') !== false;
    
    if ($isSQLite) {
        // SQLite database (development) - use Prisma table names
        $propertiesTable = 'Property';
        $usersTable = 'User';
    } else {
        // MySQL database (production) - use lowercase table names
        $propertiesTable = 'properties';
        $usersTable = 'users';
    }
    
    // For development, show all active properties
    $where_conditions = ["p.isActive = 1"];
    $params = [];
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // Get total count
    $count_query = "SELECT COUNT(*) as total FROM $propertiesTable p WHERE $where_clause";
    $count_stmt = $db->prepare($count_query);
    foreach ($params as $key => $value) {
        $count_stmt->bindValue($key, $value);
    }
    $count_stmt->execute();
    $total = $count_stmt->fetch()['total'];
    
    // Get properties with proper field names
    $ownerIdField = $isSQLite ? 'ownerId' : 'owner_id';
    $createdAtField = $isSQLite ? 'createdAt' : 'created_at';
    
    $query = "SELECT p.*, u.name as owner_name, u.email as owner_email 
              FROM $propertiesTable p 
              JOIN $usersTable u ON p.$ownerIdField = u.id 
              WHERE $where_clause 
              ORDER BY p.$createdAtField DESC 
              LIMIT :limit OFFSET :offset";
    
    $stmt = $db->prepare($query);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    
    $properties = $stmt->fetchAll();
    
    // Process properties data
    foreach ($properties as &$property) {
        $property['images'] = json_decode($property['images'], true) ?: [];
        $property['amenities'] = json_decode($property['amenities'], true) ?: [];
        $property['owner'] = [
            'name' => $property['owner_name'],
            'email' => $property['owner_email']
        ];
        unset($property['owner_name'], $property['owner_email']);
    }
    
    $total_pages = ceil($total / $limit);
    
    $response = [
        'properties' => $properties,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'total' => $total,
            'pages' => $total_pages
        ]
    ];
    
    echo json_encode($response);
}

// Test individual property API
function testPropertyDetailAPI() {
    echo "\n2. Testing Property Detail API:\n";
    
    try {
        $db = new PDO('sqlite:prisma/dev.db');
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Get first property ID
        $stmt = $db->query('SELECT id FROM Property LIMIT 1');
        $property = $stmt->fetch();
        
        if (!$property) {
            echo "   ❌ No properties found to test\n";
            return;
        }
        
        $propertyId = $property['id'];
        echo "   🔍 Testing with property ID: $propertyId\n";
        
        // Test the get property API logic
        $stmt = $db->prepare('SELECT p.*, u.name as owner_name, u.email as owner_email FROM Property p LEFT JOIN User u ON p.ownerId = u.id WHERE p.id = :id');
        $stmt->execute(['id' => $propertyId]);
        $propertyData = $stmt->fetch();
        
        if ($propertyData) {
            echo "   ✅ Property found: " . $propertyData['title'] . "\n";
            echo "   📍 Location: " . $propertyData['city'] . ", " . $propertyData['state'] . "\n";
            echo "   💰 Price: ₹" . number_format($propertyData['price']) . "\n";
            
            // Test images
            $images = json_decode($propertyData['images'], true);
            echo "   🖼️  Images: " . count($images) . " image(s)\n";
        } else {
            echo "   ❌ Property not found\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ Error: " . $e->getMessage() . "\n";
    }
}

// Run tests
testPropertiesAPI();
testPropertyDetailAPI();

echo "\n🎯 Summary:\n";
echo "✅ Sample properties created\n";
echo "✅ API endpoints updated for Prisma table names\n";
echo "✅ Property listing and detail APIs should now work\n\n";

echo "🌐 Test URLs:\n";
echo "- Frontend: http://localhost:3000/properties\n";
echo "- API: http://localhost:8000/php-backend/api/properties/index.php\n";
echo "\n💡 Make sure to start both servers:\n";
echo "1. Frontend: npm run dev\n";
echo "2. Backend: php -S localhost:8000 -t php-backend\n";
?>
