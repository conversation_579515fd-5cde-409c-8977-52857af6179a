<?php
echo "🔧 Fixing Admin Login and Adding Second Admin...\n\n";

try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to SQLite database\n\n";
    
    // First, let's fix the existing admin user password
    echo "🔑 Fixing existing admin user...\n";
    $existingAdminEmail = '<EMAIL>';
    $existingAdminPassword = 'Admin@2024!';
    $hashedPassword1 = password_hash($existingAdminPassword, PASSWORD_DEFAULT);
    
    $stmt = $db->prepare('UPDATE User SET password = :password, role = :role, isActive = 1, updatedAt = :updated WHERE email = :email');
    $stmt->execute([
        'password' => $hashedPassword1,
        'role' => 'ADMIN',
        'updated' => date('Y-m-d H:i:s'),
        'email' => $existingAdminEmail
    ]);
    
    echo "✅ Updated existing admin: $existingAdminEmail\n";
    echo "   Password: $existingAdminPassword\n\n";
    
    // Now add the second admin user
    echo "👤 Adding second admin user...\n";
    $newAdminEmail = '<EMAIL>';
    $newAdminPassword = 'new1234';
    $hashedPassword2 = password_hash($newAdminPassword, PASSWORD_DEFAULT);
    
    // Check if this admin already exists
    $stmt = $db->prepare('SELECT id FROM User WHERE email = :email');
    $stmt->execute(['email' => $newAdminEmail]);
    $existingNewAdmin = $stmt->fetch();
    
    if ($existingNewAdmin) {
        // Update existing admin
        echo "   Updating existing admin user...\n";
        $stmt = $db->prepare('UPDATE User SET password = :password, role = :role, isActive = 1, updatedAt = :updated WHERE email = :email');
        $stmt->execute([
            'password' => $hashedPassword2,
            'role' => 'ADMIN',
            'updated' => date('Y-m-d H:i:s'),
            'email' => $newAdminEmail
        ]);
        echo "✅ Updated admin user: $newAdminEmail\n";
    } else {
        // Create new admin
        echo "   Creating new admin user...\n";
        $stmt = $db->prepare('INSERT INTO User (id, name, email, password, role, isActive, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
        $adminId = 'admin_' . uniqid();
        $now = date('Y-m-d H:i:s');
        $stmt->execute([
            $adminId,
            'Second Admin User',
            $newAdminEmail,
            $hashedPassword2,
            'ADMIN',
            1,
            $now,
            $now
        ]);
        echo "✅ Created new admin user: $newAdminEmail\n";
    }
    
    echo "   Password: $newAdminPassword\n\n";
    
    // Verify both admin users
    echo "🔍 Verifying admin users...\n";
    $admins = $db->query("SELECT id, name, email, role, isActive FROM User WHERE role = 'ADMIN'")->fetchAll();
    
    foreach ($admins as $admin) {
        echo "✅ Admin: " . $admin['email'] . " (Active: " . ($admin['isActive'] ? 'Yes' : 'No') . ")\n";
    }
    
    echo "\n🎉 Admin setup complete!\n\n";
    echo "📋 Admin Login Credentials:\n";
    echo "1. Email: $existingAdminEmail\n";
    echo "   Password: $existingAdminPassword\n\n";
    echo "2. Email: $newAdminEmail\n";
    echo "   Password: $newAdminPassword\n\n";
    echo "🌐 Admin Panel URL: http://localhost:3000/admin/login\n";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}
?>
