(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[182],{5695:(e,t,s)=>{"use strict";var r=s(8999);s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},8768:(e,t,s)=>{Promise.resolve().then(s.bind(s,9584))},9584:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(5155),a=s(2115),i=s(5695);function l(){let[e,t]=(0,a.useState)(!0),[s,l]=(0,a.useState)([]),[n,c]=(0,a.useState)(""),[o,d]=(0,a.useState)("all"),p=(0,i.useRouter)();(0,a.useEffect)(()=>{(async()=>{try{let e=await fetch("/php-backend/api/auth/check-session.php",{credentials:"include"}),t=await e.json();if(!t.user||"ADMIN"!==t.user.role)return void p.push("/admin/login");await x()}catch(e){console.error("Auth check failed:",e),p.push("/admin/login")}})()},[]);let x=async()=>{try{console.log("Loading admin properties...");let t=await fetch("/php-backend/api/admin/properties.php",{credentials:"include"});if(console.log("Response status:",t.status),!t.ok)throw Error("HTTP error! status: ".concat(t.status));let s=await t.json();if(console.log("Properties API response:",s),s.success){var e;l(s.properties||[]),console.log("Loaded properties count:",(null==(e=s.properties)?void 0:e.length)||0)}else console.error("API returned success: false",s),l([])}catch(e){console.error("Failed to load properties:",e),l([])}finally{t(!1)}},h=async e=>{try{let t=await fetch("/php-backend/api/admin/approve-property.php",{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({property_id:e})}),s=await t.json();s.success?(alert("Property approved successfully!"),x()):alert("Failed to approve property: "+s.message)}catch(e){alert("Error approving property")}},u=async e=>{let t=prompt("Enter rejection reason:");if(t)try{let s=await fetch("/php-backend/api/admin/reject-property.php",{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({property_id:e,reason:t})}),r=await s.json();r.success?(alert("Property rejected successfully!"),x()):alert("Failed to reject property: "+r.message)}catch(e){alert("Error rejecting property")}},m=s.filter(e=>{var t,s,r;let a=(null==(t=e.title)?void 0:t.toLowerCase().includes(n.toLowerCase()))||(null==(s=e.owner_name)?void 0:s.toLowerCase().includes(n.toLowerCase()))||(null==(r=e.city)?void 0:r.toLowerCase().includes(n.toLowerCase())),i="all"===o||e.approval_status===o.toUpperCase();return a&&i});return e?(0,r.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading properties..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,r.jsx)("header",{className:"bg-white shadow",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Manage Properties"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Review and manage all property listings"})]}),(0,r.jsx)("div",{className:"flex space-x-4",children:(0,r.jsx)("button",{onClick:()=>p.push("/admin/dashboard"),className:"bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700",children:"Back to Dashboard"})})]})})}),(0,r.jsx)("main",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search Properties"}),(0,r.jsx)("input",{type:"text",value:n,onChange:e=>c(e.target.value),placeholder:"Search by title, owner, or city...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Filter by Status"}),(0,r.jsxs)("select",{value:o,onChange:e=>d(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"All Properties"}),(0,r.jsx)("option",{value:"pending",children:"Pending"}),(0,r.jsx)("option",{value:"approved",children:"Approved"}),(0,r.jsx)("option",{value:"rejected",children:"Rejected"})]})]})]}),(0,r.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:["Showing ",m.length," of ",s.length," properties"]})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Property"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Owner"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:m.map(e=>{var t;return(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[e.type," in ",e.city,", ",e.state]})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm text-gray-900",children:e.owner_name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.owner_email})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["₹",null==(t=e.price)?void 0:t.toLocaleString()]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat("APPROVED"===e.approval_status?"bg-green-100 text-green-800":"PENDING"===e.approval_status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:e.approval_status})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:["PENDING"===e.approval_status&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:()=>h(e.id),className:"bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700",children:"Approve"}),(0,r.jsx)("button",{onClick:()=>u(e.id),className:"bg-red-600 text-white px-3 py-1 rounded text-xs hover:bg-red-700",children:"Reject"})]}),(0,r.jsx)("a",{href:"/properties/".concat(e.id),target:"_blank",className:"bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700",children:"View"})]})})]},e.id)})})]})})}),0===m.length&&!e&&(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsxs)("div",{className:"text-gray-500",children:[(0,r.jsx)("p",{className:"text-lg font-medium mb-2",children:"No properties found"}),0===s.length?(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"mb-4",children:"No properties exist in the database yet."}),(0,r.jsx)("p",{className:"text-sm",children:"Users need to create properties first, or check the database connection."})]}):(0,r.jsx)("p",{children:"No properties match your current filter criteria."})]})})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(8768)),_N_E=e.O()}]);