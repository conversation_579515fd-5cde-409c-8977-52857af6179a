(()=>{var e={};e.id=172,e.ids=[172],e.modules={163:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"unstable_rethrow",{enumerable:!0,get:function(){return s}});let s=t(1042).unstable_rethrow;("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},899:(e,r,t)=>{"use strict";function s(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"unauthorized",{enumerable:!0,get:function(){return s}}),t(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},1042:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"unstable_rethrow",{enumerable:!0,get:function(){return function e(r){if((0,i.isNextRouterError)(r)||(0,a.isBailoutToCSRError)(r)||(0,l.isDynamicServerError)(r)||(0,o.isDynamicPostpone)(r)||(0,n.isPostpone)(r)||(0,s.isHangingPromiseRejectionError)(r))throw r;r instanceof Error&&"cause"in r&&e(r.cause)}}});let s=t(8388),n=t(2637),a=t(1846),i=t(1162),o=t(4971),l=t(8479);("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},1583:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(5239),n=t(8088),a=t(8170),i=t.n(a),o=t(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["pg",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4705)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\pg\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\pg\\[id]\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/pg/[id]/page",pathname:"/pg/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},1753:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,5814,23)),Promise.resolve().then(t.t.bind(t,6533,23)),Promise.resolve().then(t.bind(t,9190))},2765:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"notFound",{enumerable:!0,get:function(){return n}});let s=""+t(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function n(){let e=Object.defineProperty(Error(s),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=s,e}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4705:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u,generateStaticParams:()=>c});var s=t(7413),n=t(3384),a=t(4536),i=t.n(a),o=t(4),l=t(8659),d=t(9916);async function c(){return[{id:"sample-pg-1"},{id:"sample-pg-2"},{id:"sample-pg-3"}]}async function u({params:e}){let r,t=(await e).id,a=null,c=null;try{let e=await fetch(`https://housing.okayy.in/php-backend/api/properties/get.php?id=${t}`,{method:"GET",headers:{"Content-Type":"application/json"},cache:"no-store"});if(e.ok){let r=await e.json();r.success&&r.property&&"PG"===r.property.type?"APPROVED"===r.property.approval_status||"APPROVED"===r.property.approvalStatus?a=r.property:c="PG accommodation not found or not approved":c="PG accommodation not found"}else c="Failed to load PG details"}catch(e){console.error("Error fetching PG property:",e),c="PG details will be available when the site is live"}(c||!a)&&(0,d.notFound)();let u="string"==typeof a.images?JSON.parse(a.images):a.images,p="string"==typeof a.amenities?JSON.parse(a.amenities):a.amenities;return(0,s.jsxs)("main",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(o.Navbar,{}),(0,s.jsx)("section",{className:"relative",children:(0,s.jsx)("div",{className:"container-custom py-8",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:[(0,s.jsx)("div",{className:"relative h-96 lg:h-[500px] rounded-2xl overflow-hidden",children:(0,s.jsx)(n.default,{src:u[0]||"/placeholder-property.jpg",alt:a.title,fill:!0,className:"object-cover",priority:!0})}),(0,s.jsx)("div",{className:"grid grid-cols-2 gap-4",children:u.slice(1,5).map((e,r)=>(0,s.jsx)("div",{className:"relative h-44 lg:h-60 rounded-xl overflow-hidden",children:(0,s.jsx)(n.default,{src:e,alt:`${a.title} - Image ${r+2}`,fill:!0,className:"object-cover"})},r))})]})})}),(0,s.jsx)("section",{className:"py-12",children:(0,s.jsx)("div",{className:"container-custom",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-12",children:[(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-large p-8",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:a.title}),(0,s.jsxs)("div",{className:"flex items-center text-gray-600 mb-4",children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,s.jsxs)("span",{children:[a.address,", ",a.city,", ",a.state]})]}),(0,s.jsxs)("div",{className:"text-4xl font-bold text-primary mb-6",children:[(r=a.price,new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:0}).format(r)),"/month"]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6 mb-8",children:[(0,s.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-primary",children:a.view_count}),(0,s.jsx)("div",{className:"text-gray-600",children:"Views"})]}),a.area&&(0,s.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-primary",children:a.area}),(0,s.jsx)("div",{className:"text-gray-600",children:"Sq Ft"})]}),(0,s.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-xl",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-primary",children:a.type}),(0,s.jsx)("div",{className:"text-gray-600",children:"Type"})]})]}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Description"}),(0,s.jsx)("p",{className:"text-gray-600 leading-relaxed",children:a.description})]}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Amenities"}),(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:p.map((e,r)=>(0,s.jsxs)("div",{className:"flex items-center p-3 bg-gray-50 rounded-xl",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-primary mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,s.jsx)("span",{className:"text-gray-700",children:e})]},r))})]})]})}),(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-large p-6 sticky top-8",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"Contact Agent"}),(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("div",{className:"flex items-center mb-3",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-3",children:(0,s.jsx)("span",{className:"text-white font-semibold",children:a.owner_name?.charAt(0)||"A"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-semibold text-gray-900",children:a.owner_name||"N/A"}),(0,s.jsx)("div",{className:"text-gray-600 text-sm",children:"Property Agent"})]})]})}),(0,s.jsxs)("form",{className:"space-y-4",children:[(0,s.jsx)("div",{children:(0,s.jsx)("input",{type:"text",placeholder:"Your Name",className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"})}),(0,s.jsx)("div",{children:(0,s.jsx)("input",{type:"email",placeholder:"Your Email",className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"})}),(0,s.jsx)("div",{children:(0,s.jsx)("input",{type:"tel",placeholder:"Your Phone",className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"})}),(0,s.jsx)("div",{children:(0,s.jsx)("textarea",{rows:4,placeholder:"I'm interested in this property. Please contact me with more information.",className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"})}),(0,s.jsx)("button",{type:"submit",className:"w-full btn-primary",children:"Send Message"})]}),(0,s.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[(0,s.jsx)("span",{children:"Listed on:"}),(0,s.jsx)("span",{children:new Date(a.createdAt).toLocaleDateString()})]})})]})})]})})}),(0,s.jsx)("section",{className:"py-12 bg-gray-100",children:(0,s.jsxs)("div",{className:"container-custom",children:[(0,s.jsx)("h2",{className:"text-2xl md:text-3xl font-bold mb-8",children:"Similar PG Accommodations"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:(0,s.jsxs)("div",{className:"col-span-full text-center py-8",children:[(0,s.jsx)("p",{className:"text-gray-500",children:"Similar PG accommodations will be displayed here"}),(0,s.jsx)(i(),{href:"/pg/",className:"text-primary hover:underline mt-2 inline-block",children:"Browse All PG Accommodations"})]})})]})}),(0,s.jsx)(l.w,{})]})}},4777:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.t.bind(t,9603,23)),Promise.resolve().then(t.bind(t,4))},6897:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{getRedirectError:function(){return i},getRedirectStatusCodeFromError:function(){return u},getRedirectTypeFromError:function(){return c},getURLFromRedirectError:function(){return d},permanentRedirect:function(){return l},redirect:function(){return o}});let s=t(2836),n=t(9026),a=t(9121).actionAsyncStorage;function i(e,r,t){void 0===t&&(t=s.RedirectStatusCode.TemporaryRedirect);let a=Object.defineProperty(Error(n.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return a.digest=n.REDIRECT_ERROR_CODE+";"+r+";"+e+";"+t+";",a}function o(e,r){var t;throw null!=r||(r=(null==a||null==(t=a.getStore())?void 0:t.isAction)?n.RedirectType.push:n.RedirectType.replace),i(e,r,s.RedirectStatusCode.TemporaryRedirect)}function l(e,r){throw void 0===r&&(r=n.RedirectType.replace),i(e,r,s.RedirectStatusCode.PermanentRedirect)}function d(e){return(0,n.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function c(e){if(!(0,n.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function u(e){if(!(0,n.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},7576:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{ReadonlyURLSearchParams:function(){return c},RedirectType:function(){return n.RedirectType},forbidden:function(){return i.forbidden},notFound:function(){return a.notFound},permanentRedirect:function(){return s.permanentRedirect},redirect:function(){return s.redirect},unauthorized:function(){return o.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow}});let s=t(6897),n=t(9026),a=t(2765),i=t(8976),o=t(899),l=t(163);class d extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class c extends URLSearchParams{append(){throw new d}delete(){throw new d}set(){throw new d}sort(){throw new d}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},8976:(e,r,t)=>{"use strict";function s(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"forbidden",{enumerable:!0,get:function(){return s}}),t(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9916:(e,r,t)=>{"use strict";var s=t(7576);t.o(s,"notFound")&&t.d(r,{notFound:function(){return s.notFound}})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[771,814,533,940,436,722],()=>t(1583));module.exports=s})();