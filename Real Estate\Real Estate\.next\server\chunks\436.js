exports.id=436,exports.ids=[436],exports.modules={216:(e,r,t)=>{"use strict";t.d(r,{Eo:()=>d,M5:()=>n,R2:()=>o,hh:()=>l});let a="/php-backend/api";console.log("\uD83D\uDD0D API_BASE_URL:",a),console.log("\uD83D\uDD0D NODE_ENV:","production");let s={LOGIN:`${a}/auth/login.php`,SIGNUP:`${a}/auth/signup.php`,LOGOUT:`${a}/auth/logout.php`,CHECK_SESSION:`${a}/auth/check-session.php`,PROPERTIES:`${a}/properties/index.php`,PROPERTY_BY_ID:e=>`${a}/properties/get.php?id=${e}`,UPLOAD:`${a}/upload/index.php`,CONTACT:`${a}/contact/index.php`,BLOG_POSTS:`${a}/blog/index.php`,BLOG_POST_BY_SLUG:e=>`${a}/blog/get.php?slug=${e}`,USER_PROPERTIES:`${a}/user/properties.php`,USER_INQUIRIES:`${a}/user/inquiries.php`,ADMIN_PROPERTIES:`${a}/admin/properties.php`,APPROVE_PROPERTY:e=>`${a}/admin/approve.php?id=${e}`,REJECT_PROPERTY:e=>`${a}/admin/reject.php?id=${e}`},i=async(e,r={})=>{let t={headers:{"Content-Type":"application/json"},credentials:"include"},a={...t,...r,headers:{...t.headers,...r.headers}};try{let r;console.log("\uD83D\uDD0D API Request:",{url:e,options:a});let t=await fetch(e,a);console.log("\uD83D\uDD0D API Response:",{status:t.status,ok:t.ok,headers:Object.fromEntries(t.headers.entries())});let s=await t.text();console.log("\uD83D\uDD0D Raw Response:",s);try{r=JSON.parse(s),console.log("\uD83D\uDD0D Parsed Data:",r)}catch(e){throw console.error("\uD83D\uDD0D JSON Parse Error:",e),Error(`Invalid JSON response: ${s}`)}if(!t.ok)throw Error(r.error||`HTTP error! status: ${t.status}`);return r}catch(e){throw console.error("\uD83D\uDD0D API request failed:",e),e}},o={login:async(e,r)=>i(s.LOGIN,{method:"POST",body:JSON.stringify({email:e,password:r})}),signup:async(e,r,t,a)=>i(s.SIGNUP,{method:"POST",body:JSON.stringify({name:e,email:r,password:t,phone:a})}),logout:async()=>i(s.LOGOUT,{method:"POST"}),checkSession:async()=>i(s.CHECK_SESSION)},n={getProperties:async(e={})=>{let r=new URLSearchParams;return Object.entries(e).forEach(([e,t])=>{null!=t&&""!==t&&r.append(e,t.toString())}),i(`${s.PROPERTIES}?${r.toString()}`)},createProperty:async e=>i(s.PROPERTIES,{method:"POST",body:JSON.stringify(e)}),getPropertyById:async e=>i(s.PROPERTY_BY_ID(e))},l={getPosts:async(e={})=>{let r=new URLSearchParams;return Object.entries(e).forEach(([e,t])=>{null!=t&&""!==t&&r.append(e,t.toString())}),i(`${s.BLOG_POSTS}?${r.toString()}`)},getPostBySlug:async e=>i(s.BLOG_POST_BY_SLUG(e))},d={getProperties:async()=>i(s.USER_PROPERTIES),getInquiries:async()=>i(s.USER_INQUIRIES)}},1135:()=>{},2744:()=>{},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n,metadata:()=>o});var a=t(7413),s=t(5091),i=t.n(s);t(1135);let o={title:{default:"Real Estate India - Buy, Sell, and Rent Properties",template:"%s | Real Estate India"},description:"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities, or list your property with us. Expert guidance for all your property needs.",keywords:["real estate India","property for sale","property for rent","buy property","sell property","apartments","houses","villas","commercial property"],authors:[{name:"Real Estate India"}],creator:"Real Estate India",publisher:"Real Estate India",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("https://realestate-india.com"),alternates:{canonical:"/"},openGraph:{type:"website",locale:"en_IN",url:"https://realestate-india.com",title:"Real Estate India - Buy, Sell, and Rent Properties",description:"Find your dream home in India with our comprehensive real estate platform. Browse properties for sale and rent across major cities.",siteName:"Real Estate India"},twitter:{card:"summary_large_image",title:"Real Estate India - Buy, Sell, and Rent Properties",description:"Find your dream home in India with our comprehensive real estate platform.",creator:"@realestateindia"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function n({children:e}){return(0,a.jsx)("html",{lang:"en",className:"scroll-smooth",children:(0,a.jsx)("body",{className:`${i().variable} font-sans bg-background text-text-primary antialiased`,children:e})})}},8031:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},8279:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},9190:(e,r,t)=>{"use strict";t.d(r,{Navbar:()=>l});var a=t(687),s=t(3210),i=t(5814),o=t.n(i),n=t(216);function l(){let[e,r]=(0,s.useState)(!1),[t,i]=(0,s.useState)({user:null});return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("nav",{className:"bg-white/95 backdrop-blur-md shadow-soft sticky top-0 z-50 border-b border-gray-100",children:(0,a.jsxs)("div",{className:"container-custom py-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)(o(),{href:"/",className:"flex items-center space-x-3 group",children:[(0,a.jsx)("div",{className:"relative w-12 h-12 p-2 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl shadow-soft group-hover:shadow-colored transition-all duration-300 transform group-hover:scale-105"}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-xl font-bold text-gradient",children:"RealEstate"}),(0,a.jsx)("span",{className:"text-xs text-text-tertiary font-medium",children:"India"})]})]}),(0,a.jsxs)("div",{className:"hidden lg:flex items-center space-x-1",children:[(0,a.jsx)(o(),{href:"/",className:"px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",children:"Home"}),(0,a.jsx)(o(),{href:"/buy",className:"px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",children:"Buy"}),(0,a.jsx)(o(),{href:"/rent",className:"px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",children:"Rent"}),(0,a.jsx)(o(),{href:"/pg",className:"px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",children:"PG"}),(0,a.jsx)(o(),{href:"/sell",className:"px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",children:"Sell"})]}),(0,a.jsx)("div",{className:"hidden lg:flex items-center space-x-3",children:t.user?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o(),{href:"/dashboard/",className:"px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",children:"Dashboard"}),"ADMIN"===t.user.role&&(0,a.jsx)(o(),{href:"/admin",className:"px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",children:"Admin Dashboard"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 px-4 py-2 bg-gray-50 rounded-xl",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-semibold",children:t.user.name?.charAt(0).toUpperCase()||"U"})}),(0,a.jsxs)("span",{className:"text-text-secondary font-medium",children:["Hi, ",t.user.name||"User"]})]}),(0,a.jsx)("button",{onClick:async()=>{try{await n.R2.logout(),i({user:null}),window.location.href="/"}catch(e){console.error("Logout error:",e)}},className:"px-4 py-2 text-text-secondary hover:text-error-600 hover:bg-error-50 font-medium rounded-xl transition-all duration-300",children:"Logout"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o(),{href:"/login/",className:"px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",children:"Login"}),(0,a.jsx)(o(),{href:"/signup/",className:"btn-primary",children:"Sign Up"})]})}),(0,a.jsx)("button",{className:"lg:hidden p-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",onClick:()=>r(!e),children:e?(0,a.jsx)("span",{children:"X"}):(0,a.jsx)("span",{children:"☰"})})]}),e&&(0,a.jsx)("div",{className:"lg:hidden mt-6 animate-slide-down",children:(0,a.jsx)("div",{className:"bg-white/95 backdrop-blur-md rounded-2xl shadow-large border border-gray-100 p-6",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,a.jsx)(o(),{href:"/",className:"px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",onClick:()=>r(!1),children:"Home"}),(0,a.jsx)(o(),{href:"/buy",className:"px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",onClick:()=>r(!1),children:"Buy"}),(0,a.jsx)(o(),{href:"/rent",className:"px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",onClick:()=>r(!1),children:"Rent"}),(0,a.jsx)(o(),{href:"/pg",className:"px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",onClick:()=>r(!1),children:"PG"}),(0,a.jsx)(o(),{href:"/sell",className:"px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",onClick:()=>r(!1),children:"Sell"}),(0,a.jsx)("div",{className:"pt-4 mt-4 border-t border-gray-200 flex flex-col space-y-2",children:t.user?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o(),{href:"/dashboard/",className:"px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",onClick:()=>r(!1),children:"Dashboard"}),"ADMIN"===t.user.role&&(0,a.jsx)(o(),{href:"/admin",className:"px-4 py-3 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",onClick:()=>r(!1),children:"Admin Dashboard"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 px-4 py-3 bg-gray-50 rounded-xl",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-semibold",children:t.user.name?.charAt(0).toUpperCase()||"U"})}),(0,a.jsxs)("span",{className:"text-text-secondary font-medium",children:["Hi, ",t.user.name||"User"]})]}),(0,a.jsx)("button",{onClick:async()=>{try{await n.R2.logout(),i({user:null}),window.location.href="/"}catch(e){console.error("Logout error:",e)}},className:"px-4 py-2 text-text-secondary hover:text-error-600 hover:bg-error-50 font-medium rounded-xl transition-all duration-300",children:"Logout"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o(),{href:"/login/",className:"px-4 py-2 text-text-primary hover:text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300",onClick:()=>r(!1),children:"Login"}),(0,a.jsx)(o(),{href:"/signup/",className:"btn-primary",children:"Sign Up"})]})})]})})})]})})})}},9592:()=>{}};