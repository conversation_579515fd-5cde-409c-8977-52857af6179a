-- =====================================================
-- UPDATED COMPLETE DATABASE SETUP SQL
-- For housing.okayy.in Real Estate Website
-- FIXED: Admin users, Blog posts, Properties compatibility
-- Date: 2025-01-19
-- =====================================================

-- NOTE: Create the database through your hosting control panel first
-- Database name: u357173570_housingokayy (or your actual database name)

-- =====================================================
-- 1. USERS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255),
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('USER', 'ADMIN') DEFAULT 'USER',
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =====================================================
-- 2. PROPERTIES TABLE (Updated with all enum values)
-- =====================================================
CREATE TABLE IF NOT EXISTS properties (
    id VARCHAR(36) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    price INT NOT NULL,
    currency VARCHAR(10) DEFAULT 'INR',
    type ENUM('APARTMENT', 'HOUSE', 'VILLA', 'PLOT', 'COMMERCIAL', 'OFFICE', 'PG') NOT NULL,
    listing_type ENUM('RENT', 'SALE') DEFAULT 'SALE',
    accommodation_type ENUM('FULL_HOUSE', 'FLAT', 'ONE_BHK', 'TWO_BHK', 'THREE_BHK', 'FOUR_BHK', 'FAMILY', 'BACHELOR'),
    pg_room_type ENUM('SINGLE', 'DOUBLE', 'TRIPLE', 'FOUR_SHARING', 'DORMITORY'),
    pg_gender_preference ENUM('MALE', 'FEMALE', 'MIXED'),
    status ENUM('AVAILABLE', 'SOLD', 'RENTED', 'PENDING') DEFAULT 'AVAILABLE',
    bedrooms INT,
    bathrooms INT,
    area INT,
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100) NOT NULL,
    pincode VARCHAR(10) NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    images TEXT, -- JSON string
    amenities TEXT, -- JSON string
    is_featured BOOLEAN DEFAULT FALSE,
    is_approved BOOLEAN DEFAULT FALSE,
    approval_status ENUM('PENDING', 'APPROVED', 'REJECTED') DEFAULT 'PENDING',
    rejection_reason TEXT,
    admin_notes TEXT,
    view_count INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    owner_id VARCHAR(36) NOT NULL,
    approved_by VARCHAR(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    approved_at TIMESTAMP NULL,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
);

-- =====================================================
-- 3. SAVED PROPERTIES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS saved_properties (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    property_id VARCHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE,
    UNIQUE KEY unique_save (user_id, property_id)
);

-- =====================================================
-- 4. INQUIRIES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS inquiries (
    id VARCHAR(36) PRIMARY KEY,
    property_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    message TEXT,
    status ENUM('NEW', 'CONTACTED', 'QUALIFIED', 'CLOSED') DEFAULT 'NEW',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- =====================================================
-- 5. CONTACT MESSAGES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS contact_messages (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    message TEXT NOT NULL,
    type ENUM('GENERAL', 'VALUATION', 'INQUIRY') DEFAULT 'GENERAL',
    status ENUM('NEW', 'READ', 'REPLIED') DEFAULT 'NEW',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 6. BLOG POSTS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS blog_posts (
    id VARCHAR(36) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    featured_image VARCHAR(500),
    published BOOLEAN DEFAULT TRUE,
    tags TEXT, -- JSON string
    category VARCHAR(100),
    author_id VARCHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE
);

-- =====================================================
-- 7. USER SESSIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- =====================================================
-- 8. CREATE INDEXES FOR PERFORMANCE
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_properties_type ON properties(type);
CREATE INDEX IF NOT EXISTS idx_properties_city ON properties(city);
CREATE INDEX IF NOT EXISTS idx_properties_status ON properties(status);
CREATE INDEX IF NOT EXISTS idx_properties_approval ON properties(approval_status);
CREATE INDEX IF NOT EXISTS idx_properties_owner ON properties(owner_id);
CREATE INDEX IF NOT EXISTS idx_properties_featured ON properties(is_featured);
CREATE INDEX IF NOT EXISTS idx_inquiries_property ON inquiries(property_id);
CREATE INDEX IF NOT EXISTS idx_inquiries_user ON inquiries(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_sessions_user ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_blog_published ON blog_posts(published);
CREATE INDEX IF NOT EXISTS idx_blog_slug ON blog_posts(slug);
CREATE INDEX IF NOT EXISTS idx_blog_category ON blog_posts(category);

-- =====================================================
-- 9. INSERT ADMIN USERS (FIXED)
-- =====================================================
-- Primary Admin: <EMAIL> | Password: Admin@2024!
-- Secondary Admin: <EMAIL> | Password: new1234

INSERT INTO users (id, name, email, password, role, is_active, created_at, updated_at) VALUES
('admin-housing-okayy', 'Admin User', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'ADMIN', TRUE, NOW(), NOW()),
('admin-okayy-secondary', 'Second Admin User', '<EMAIL>', '$2y$10$N9qo8uLOickgx2ZMRZoMye/Lo2o7cFWUNv/ovrzI4cOOdQap.Ru.', 'ADMIN', TRUE, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    password = VALUES(password),
    role = 'ADMIN',
    is_active = TRUE,
    updated_at = NOW();

-- =====================================================
-- 10. INSERT SAMPLE PROPERTIES (FIXED)
-- =====================================================
INSERT INTO properties (
    id, title, description, price, currency, type, listing_type, accommodation_type,
    bedrooms, bathrooms, area, address, city, state, pincode,
    images, amenities, is_featured, is_approved, approval_status,
    view_count, is_active, owner_id, approved_by, created_at, approved_at
) VALUES 
(
    'prop_sample_001',
    'Luxury 3BHK Apartment in Bandra West',
    'Spacious 3BHK apartment with modern amenities, sea view, and prime location in Bandra West. Perfect for families looking for comfort and convenience.',
    25000000,
    'INR',
    'APARTMENT',
    'SALE',
    'FLAT',
    3,
    2,
    1200,
    '123 Hill Road, Bandra West',
    'Mumbai',
    'Maharashtra',
    '400050',
    '["https://images.unsplash.com/photo-1560518883-ce09059eeffa?q=80&w=800", "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?q=80&w=800"]',
    '["Swimming Pool", "Gym", "Parking", "Security", "Elevator", "Garden"]',
    TRUE,
    TRUE,
    'APPROVED',
    0,
    TRUE,
    'admin-housing-okayy',
    'admin-housing-okayy',
    NOW(),
    NOW()
),
(
    'prop_sample_002',
    'Modern 2BHK Villa in Gurgaon',
    'Beautiful 2BHK villa with garden, modern kitchen, and excellent connectivity to Delhi NCR. Ideal for small families.',
    8500000,
    'INR',
    'VILLA',
    'SALE',
    'FULL_HOUSE',
    2,
    2,
    1500,
    'Sector 45, DLF Phase 2',
    'Gurgaon',
    'Haryana',
    '122002',
    '["https://images.unsplash.com/photo-1564013799919-ab600027ffc6?q=80&w=800", "https://images.unsplash.com/photo-1570129477492-45c003edd2be?q=80&w=800"]',
    '["Garden", "Parking", "Security", "Power Backup"]',
    FALSE,
    TRUE,
    'APPROVED',
    0,
    TRUE,
    'admin-housing-okayy',
    'admin-housing-okayy',
    NOW(),
    NOW()
),
(
    'prop_sample_003',
    'Affordable 1BHK Flat for Rent',
    'Cozy 1BHK flat available for rent in a prime location. Fully furnished with all modern amenities.',
    25000,
    'INR',
    'APARTMENT',
    'RENT',
    'FLAT',
    1,
    1,
    600,
    'Koramangala 4th Block',
    'Bangalore',
    'Karnataka',
    '560034',
    '["https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?q=80&w=800", "https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?q=80&w=800"]',
    '["Furnished", "WiFi", "Parking", "Security"]',
    FALSE,
    TRUE,
    'APPROVED',
    0,
    TRUE,
    'admin-housing-okayy',
    'admin-housing-okayy',
    NOW(),
    NOW()
),
(
    'prop_sample_004',
    'Premium PG for Working Professionals',
    'Premium PG accommodation with all meals included. Perfect for working professionals and students.',
    15000,
    'INR',
    'PG',
    'RENT',
    NULL,
    1,
    1,
    200,
    'HSR Layout Sector 1',
    'Bangalore',
    'Karnataka',
    '560102',
    '["https://images.unsplash.com/photo-1555854877-bab0e564b8d5?q=80&w=800"]',
    '["Food Included", "WiFi", "Laundry", "AC", "Security"]',
    FALSE,
    TRUE,
    'APPROVED',
    0,
    TRUE,
    'admin-housing-okayy',
    'admin-housing-okayy',
    NOW(),
    NOW()
);

-- =====================================================
-- 11. INSERT SAMPLE BLOG POSTS (FIXED)
-- =====================================================
INSERT INTO blog_posts (id, title, slug, content, excerpt, featured_image, published, tags, category, author_id) VALUES
(
    'blog_001',
    'Top 10 Areas to Buy Property in Hyderabad 2024',
    'top-10-areas-buy-property-hyderabad-2024',
    '<h2>Best Investment Areas in Hyderabad</h2><p>Hyderabad has emerged as one of India''s most promising real estate markets. Here are the top 10 areas to consider for property investment in 2024:</p><h3>1. Gachibowli</h3><p>Known as the IT hub of Hyderabad, Gachibowli offers excellent connectivity and modern infrastructure.</p><h3>2. Kondapur</h3><p>A rapidly developing area with great potential for appreciation.</p><h3>3. Manikonda</h3><p>Affordable housing options with good connectivity to IT corridors.</p><h3>4. Narsingi</h3><p>Emerging locality with excellent infrastructure development.</p><h3>5. Kokapet</h3><p>Premium residential area with luxury projects.</p><p>These areas offer the best combination of infrastructure, connectivity, and growth potential for property investors.</p>',
    'Discover the most promising areas in Hyderabad for property investment in 2024. From IT hubs to emerging localities, find your perfect investment opportunity.',
    'https://images.unsplash.com/photo-1582407947304-fd86f028f716?q=80&w=800',
    TRUE,
    '["real estate", "hyderabad", "investment", "property"]',
    'Investment Guide',
    'admin-housing-okayy'
),
(
    'blog_002',
    'Complete Guide to Property Registration Process in India',
    'property-registration-process-india',
    '<h2>Understanding Property Registration in India</h2><p>Property registration is a crucial legal process that establishes your ownership rights. Here''s a comprehensive guide:</p><h3>Required Documents</h3><ul><li>Sale Deed</li><li>Property Title Documents</li><li>NOC from Society/Builder</li><li>Property Tax Receipts</li><li>Identity and Address Proof</li></ul><h3>Registration Process</h3><ol><li>Document Verification</li><li>Payment of Stamp Duty</li><li>Registration Fee Payment</li><li>Biometric Verification</li><li>Document Registration</li></ol><h3>Important Tips</h3><p>Always verify the property''s legal status before registration. Ensure all documents are genuine and complete.</p>',
    'A step-by-step guide to property registration in India. Learn about required documents, fees, and the complete process.',
    'https://images.unsplash.com/photo-1450101499163-c8848c66ca85?q=80&w=800',
    TRUE,
    '["property registration", "legal", "documentation", "india"]',
    'Legal Guide',
    'admin-housing-okayy'
),
(
    'blog_003',
    'Home Loan Tips: How to Get the Best Interest Rates in 2024',
    'home-loan-tips-best-interest-rates',
    '<h2>Getting the Best Home Loan Rates</h2><p>Securing a home loan with favorable interest rates can save you lakhs over the loan tenure. Here are expert tips:</p><h3>Improve Your Credit Score</h3><p>A credit score above 750 significantly improves your chances of getting better rates.</p><h3>Compare Multiple Lenders</h3><p>Don''t settle for the first offer. Compare rates from banks, NBFCs, and housing finance companies.</p><h3>Consider Loan Tenure Carefully</h3><p>While longer tenure reduces EMI, it increases total interest paid.</p><h3>Make a Higher Down Payment</h3><p>A larger down payment reduces the loan amount and can help negotiate better rates.</p><p>Research thoroughly and negotiate with lenders for the best deal.</p>',
    'Expert tips to secure the best home loan interest rates in 2024. Learn how to save money on your home loan.',
    'https://images.unsplash.com/photo-**********-6726b3ff858f?q=80&w=800',
    TRUE,
    '["home loan", "interest rates", "finance", "tips"]',
    'Finance Guide',
    'admin-housing-okayy'
);

-- =====================================================
-- 12. VERIFICATION QUERIES
-- =====================================================
-- Verify admin users were created
SELECT 'Admin users verification:' as info;
SELECT id, name, email, role, is_active, created_at FROM users WHERE role = 'ADMIN';

-- Show table counts
SELECT 'Database setup completed!' as status;
SELECT
    (SELECT COUNT(*) FROM users) as total_users,
    (SELECT COUNT(*) FROM users WHERE role = 'ADMIN') as admin_users,
    (SELECT COUNT(*) FROM properties) as total_properties,
    (SELECT COUNT(*) FROM blog_posts) as total_blog_posts;

-- =====================================================
-- SETUP COMPLETE!
-- =====================================================
-- Admin Login Credentials:
-- 1. Primary Admin:
--    Email: <EMAIL>
--    Password: Admin@2024!
--
-- 2. Secondary Admin:
--    Email: <EMAIL>
--    Password: new1234
--
-- Admin Panel URL: https://yourdomain.com/admin/login
--
-- Sample Data Included:
-- - 2 Admin users
-- - 4 Sample properties (all approved)
-- - 3 Blog posts
-- =====================================================
