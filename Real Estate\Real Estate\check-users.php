<?php
echo "🔍 Checking existing users...\n\n";

try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to SQLite database\n\n";
    
    // Get all users
    $users = $db->query('SELECT id, name, email, role, isActive FROM User')->fetchAll();
    
    echo "📋 Current users in database:\n";
    echo "Total users: " . count($users) . "\n\n";
    
    foreach ($users as $user) {
        echo "ID: " . $user['id'] . "\n";
        echo "Name: " . ($user['name'] ?: 'N/A') . "\n";
        echo "Email: " . $user['email'] . "\n";
        echo "Role: " . $user['role'] . "\n";
        echo "Active: " . ($user['isActive'] ? 'Yes' : 'No') . "\n";
        echo "---\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}
?>
