(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[733],{2899:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(5155),r=t(2115),i=t(5695);function l(){let[e,s]=(0,r.useState)(!0),[t,l]=(0,r.useState)([]),[n,c]=(0,r.useState)(""),[d,o]=(0,r.useState)("all"),x=(0,i.useRouter)();(0,r.useEffect)(()=>{(async()=>{try{let e=await fetch("/php-backend/api/auth/check-session.php",{credentials:"include"}),s=await e.json();if(!s.user||"ADMIN"!==s.user.role)return void x.push("/admin/login");await u()}catch(e){console.error("Auth check failed:",e),x.push("/admin/login")}})()},[]);let u=async()=>{try{let e=await fetch("/php-backend/api/admin/users.php",{credentials:"include"}),s=await e.json();s.success&&l(s.users||[])}catch(e){console.error("Failed to load users:",e)}finally{s(!1)}},p=async(e,s)=>{try{let t=await fetch("/php-backend/api/admin/toggle-user-status.php",{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({user_id:e,is_active:!s})}),a=await t.json();a.success?(alert("User ".concat(s?"deactivated":"activated"," successfully!")),u()):alert("Failed to update user status: "+a.message)}catch(e){alert("Error updating user status")}},h=async(e,s)=>{if(confirm('Are you sure you want to delete user "'.concat(s,'"? This action cannot be undone.')))try{let s=await fetch("/php-backend/api/admin/delete-user.php",{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({user_id:e})}),t=await s.json();t.success?(alert("User deleted successfully!"),u()):alert("Failed to delete user: "+t.message)}catch(e){alert("Error deleting user")}},m=t.filter(e=>{var s,t;let a=(null==(s=e.name)?void 0:s.toLowerCase().includes(n.toLowerCase()))||(null==(t=e.email)?void 0:t.toLowerCase().includes(n.toLowerCase())),r="all"===d||e.role===d.toUpperCase();return a&&r});return e?(0,a.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading users..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,a.jsx)("header",{className:"bg-white shadow",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Manage Users"}),(0,a.jsx)("p",{className:"text-gray-600",children:"View and manage all user accounts"})]}),(0,a.jsx)("div",{className:"flex space-x-4",children:(0,a.jsx)("button",{onClick:()=>x.push("/admin/dashboard"),className:"bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700",children:"Back to Dashboard"})})]})})}),(0,a.jsx)("main",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search Users"}),(0,a.jsx)("input",{type:"text",value:n,onChange:e=>c(e.target.value),placeholder:"Search by name or email...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Filter by Role"}),(0,a.jsxs)("select",{value:d,onChange:e=>o(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Users"}),(0,a.jsx)("option",{value:"user",children:"Regular Users"}),(0,a.jsx)("option",{value:"admin",children:"Administrators"})]})]})]}),(0,a.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:["Showing ",m.length," of ",t.length," users"]})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Properties"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Joined"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:m.map(e=>{var s,t;return(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3",children:(0,a.jsx)("span",{className:"text-white font-semibold",children:(null==(t=e.name)||null==(s=t.charAt(0))?void 0:s.toUpperCase())||"U"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name||"Unknown"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat("ADMIN"===e.role?"bg-purple-100 text-purple-800":"bg-blue-100 text-blue-800"),children:e.role})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:["Total: ",e.property_count||0]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Approved: ",e.approved_properties||0," | Pending: ",e.pending_properties||0]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(e.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.is_active?"Active":"Inactive"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString()}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:["ADMIN"!==e.role&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>p(e.id,e.is_active),className:"px-3 py-1 rounded text-xs ".concat(e.is_active?"bg-red-600 text-white hover:bg-red-700":"bg-green-600 text-white hover:bg-green-700"),children:e.is_active?"Deactivate":"Activate"}),(0,a.jsx)("button",{onClick:()=>h(e.id,e.name),className:"bg-red-600 text-white px-3 py-1 rounded text-xs hover:bg-red-700",children:"Delete"})]}),"ADMIN"===e.role&&(0,a.jsx)("span",{className:"text-gray-400 text-xs",children:"Protected"})]})})]},e.id)})})]})})}),0===m.length&&(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsx)("p",{className:"text-gray-500",children:"No users found matching your criteria."})})]})})]})}},4033:(e,s,t)=>{Promise.resolve().then(t.bind(t,2899))},5695:(e,s,t)=>{"use strict";var a=t(8999);t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})}},e=>{var s=s=>e(e.s=s);e.O(0,[441,684,358],()=>s(4033)),_N_E=e.O()}]);