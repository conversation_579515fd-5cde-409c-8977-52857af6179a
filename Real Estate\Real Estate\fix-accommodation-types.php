<?php
echo "🔧 Fixing Accommodation Types...\n\n";

try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to SQLite database\n\n";
    
    // Check current accommodation types
    $properties = $db->query('SELECT id, title, accommodationType FROM Property')->fetchAll();
    
    echo "📋 Current properties:\n";
    foreach ($properties as $property) {
        echo "- " . $property['title'] . " | Type: " . ($property['accommodationType'] ?: 'NULL') . "\n";
    }
    
    echo "\n🔄 Updating accommodation types...\n";
    
    // Update properties with proper accommodation types
    $updates = [
        // For apartments and villas, use appropriate types
        'APARTMENT' => 'FLAT',
        'VILLA' => 'FULL_HOUSE',
        'HOUSE' => 'FULL_HOUSE'
    ];
    
    // Update based on property type
    $stmt = $db->prepare('UPDATE Property SET accommodationType = ? WHERE type = ?');
    
    foreach ($updates as $propertyType => $accommodationType) {
        $stmt->execute([$accommodationType, $propertyType]);
        echo "✅ Updated $propertyType properties to $accommodationType\n";
    }
    
    // For PG properties, set to NULL (they don't need accommodation type)
    $db->exec('UPDATE Property SET accommodationType = NULL WHERE type = "PG"');
    echo "✅ Set PG properties accommodation type to NULL\n";
    
    echo "\n📋 Updated properties:\n";
    $updatedProperties = $db->query('SELECT id, title, type, accommodationType FROM Property')->fetchAll();
    
    foreach ($updatedProperties as $property) {
        echo "- " . $property['title'] . " (" . $property['type'] . ") | Accommodation: " . ($property['accommodationType'] ?: 'NULL') . "\n";
    }
    
    echo "\n🎉 Accommodation types fixed!\n";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}
?>
