<?php
echo "🏠 Creating Sample Properties...\n\n";

try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to SQLite database\n\n";
    
    // Get admin user ID
    $adminStmt = $db->prepare('SELECT id FROM User WHERE role = "ADMIN" LIMIT 1');
    $adminStmt->execute();
    $admin = $adminStmt->fetch();
    
    if (!$admin) {
        echo "❌ No admin user found. Please create admin user first.\n";
        exit(1);
    }
    
    $adminId = $admin['id'];
    echo "👤 Using admin user ID: $adminId\n\n";
    
    // Sample properties data
    $properties = [
        [
            'id' => 'prop_' . uniqid(),
            'title' => 'Luxury 3BHK Apartment in Bandra West',
            'description' => 'Beautiful 3BHK apartment with modern amenities, sea view, and prime location in Bandra West. Perfect for families looking for luxury living.',
            'price' => 8500000,
            'currency' => 'INR',
            'type' => 'APARTMENT',
            'listingType' => 'SALE',
            'accommodationType' => 'FAMILY',
            'bedrooms' => 3,
            'bathrooms' => 2,
            'area' => 1200,
            'address' => '123 Hill Road, Bandra West',
            'city' => 'Mumbai',
            'state' => 'Maharashtra',
            'pincode' => '400050',
            'images' => json_encode(['https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800', 'https://images.unsplash.com/photo-1560448075-bb485b067938?w=800']),
            'amenities' => json_encode(['Swimming Pool', 'Gym', 'Parking', 'Security', 'Elevator']),
            'isFeatured' => 1,
            'isApproved' => 1,
            'approvalStatus' => 'APPROVED',
            'isActive' => 1,
            'ownerId' => $adminId,
            'viewCount' => 0,
            'createdAt' => date('Y-m-d H:i:s'),
            'updatedAt' => date('Y-m-d H:i:s')
        ],
        [
            'id' => 'prop_' . uniqid(),
            'title' => 'Modern 2BHK Villa in Gurgaon',
            'description' => 'Spacious 2BHK villa with garden, modern kitchen, and excellent connectivity to Delhi NCR. Ideal for small families.',
            'price' => 6500000,
            'currency' => 'INR',
            'type' => 'VILLA',
            'listingType' => 'SALE',
            'accommodationType' => 'FAMILY',
            'bedrooms' => 2,
            'bathrooms' => 2,
            'area' => 1500,
            'address' => 'Sector 45, DLF Phase 2',
            'city' => 'Gurgaon',
            'state' => 'Haryana',
            'pincode' => '122002',
            'images' => json_encode(['https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800', 'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800']),
            'amenities' => json_encode(['Garden', 'Parking', 'Security', 'Power Backup']),
            'isFeatured' => 0,
            'isApproved' => 1,
            'approvalStatus' => 'APPROVED',
            'isActive' => 1,
            'ownerId' => $adminId,
            'viewCount' => 0,
            'createdAt' => date('Y-m-d H:i:s'),
            'updatedAt' => date('Y-m-d H:i:s')
        ],
        [
            'id' => 'prop_' . uniqid(),
            'title' => 'Affordable 1BHK Flat for Rent',
            'description' => 'Cozy 1BHK flat available for rent in a prime location. Fully furnished with all modern amenities.',
            'price' => 25000,
            'currency' => 'INR',
            'type' => 'APARTMENT',
            'listingType' => 'RENT',
            'accommodationType' => 'BACHELOR',
            'bedrooms' => 1,
            'bathrooms' => 1,
            'area' => 600,
            'address' => 'Koramangala 4th Block',
            'city' => 'Bangalore',
            'state' => 'Karnataka',
            'pincode' => '560034',
            'images' => json_encode(['https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=800', 'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800']),
            'amenities' => json_encode(['Furnished', 'WiFi', 'Parking', 'Security']),
            'isFeatured' => 0,
            'isApproved' => 1,
            'approvalStatus' => 'APPROVED',
            'isActive' => 1,
            'ownerId' => $adminId,
            'viewCount' => 0,
            'createdAt' => date('Y-m-d H:i:s'),
            'updatedAt' => date('Y-m-d H:i:s')
        ],
        [
            'id' => 'prop_' . uniqid(),
            'title' => 'Premium PG for Working Professionals',
            'description' => 'Premium PG accommodation with all meals included. Perfect for working professionals and students.',
            'price' => 15000,
            'currency' => 'INR',
            'type' => 'PG',
            'listingType' => 'RENT',
            'pgRoomType' => 'SINGLE',
            'pgGenderPreference' => 'MALE',
            'bedrooms' => 1,
            'bathrooms' => 1,
            'area' => 200,
            'address' => 'HSR Layout Sector 1',
            'city' => 'Bangalore',
            'state' => 'Karnataka',
            'pincode' => '560102',
            'images' => json_encode(['https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800']),
            'amenities' => json_encode(['Food Included', 'WiFi', 'Laundry', 'AC', 'Security']),
            'isFeatured' => 0,
            'isApproved' => 1,
            'approvalStatus' => 'APPROVED',
            'isActive' => 1,
            'ownerId' => $adminId,
            'viewCount' => 0,
            'createdAt' => date('Y-m-d H:i:s'),
            'updatedAt' => date('Y-m-d H:i:s')
        ]
    ];
    
    // Insert properties
    $insertedCount = 0;
    foreach ($properties as $property) {
        try {
            $columns = implode(', ', array_keys($property));
            $placeholders = ':' . implode(', :', array_keys($property));
            
            $query = "INSERT INTO Property ($columns) VALUES ($placeholders)";
            $stmt = $db->prepare($query);
            
            foreach ($property as $key => $value) {
                $stmt->bindValue(":$key", $value);
            }
            
            if ($stmt->execute()) {
                echo "✅ Created: " . $property['title'] . "\n";
                $insertedCount++;
            } else {
                echo "❌ Failed to create: " . $property['title'] . "\n";
            }
        } catch (Exception $e) {
            echo "❌ Error creating " . $property['title'] . ": " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n🎉 Successfully created $insertedCount sample properties!\n\n";
    
    // Verify properties were created
    $count = $db->query('SELECT COUNT(*) as count FROM Property')->fetch();
    echo "📊 Total properties in database: " . $count['count'] . "\n";
    
    echo "\n🌐 Now you can test:\n";
    echo "- Property listings: http://localhost:3000/properties\n";
    echo "- Property details: http://localhost:3000/properties/[property-id]\n";
    echo "- Buy page: http://localhost:3000/buy\n";
    echo "- Rent page: http://localhost:3000/rent\n";
    echo "- PG page: http://localhost:3000/pg\n";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}
?>
