(()=>{var e={};e.id=88,e.ids=[88],e.modules={474:(e,t,s)=>{"use strict";s.d(t,{default:()=>a.a});var r=s(1261),a=s.n(r)},730:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var r=s(687),a=s(3210),n=s(9190),l=s(1317),i=s(2540),o=s(3870),c=s(5814),d=s.n(c),h=s(474);function x(){let[e,t]=(0,a.useState)({type:"",minPrice:"",maxPrice:"",bedrooms:"",city:""}),[s,c]=(0,a.useState)([]),[x,m]=(0,a.useState)(!1),[u,p]=(0,a.useState)({currentPage:1,totalPages:1,totalItems:0,hasNext:!1,hasPrev:!1}),f=async(t=e,s=1)=>{m(!0);try{let e=Object.entries(t).reduce((e,[t,s])=>(s&&""!==s.trim()&&(e[t]=s),e),{}),r=new URLSearchParams({page:s.toString(),limit:"12",listingType:"RENT",...e}).toString(),a=await fetch(`/php-backend/api/properties/search.php?${r}`);if(!a.ok)throw Error(`HTTP error! status: ${a.status}`);let n=await a.json();c(n.properties),p(n.pagination)}catch(e){console.error("Error loading properties:",e)}finally{m(!1)}},g=(0,a.useCallback)(e=>{t(e),f(e,1)},[]);return(0,r.jsxs)("main",{className:"min-h-screen",children:[(0,r.jsx)(n.Navbar,{}),(0,r.jsxs)("section",{className:"relative h-[500px] flex items-center justify-center",children:[(0,r.jsx)("div",{className:"absolute inset-0 z-0",children:(0,r.jsx)(h.default,{src:"https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?q=80&w=1470&auto=format&fit=crop",alt:"Rent Properties",fill:!0,className:"object-cover brightness-50"})}),(0,r.jsxs)("div",{className:"container-custom relative z-10 text-center text-white",children:[(0,r.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-6",children:"Find Your Perfect Rental"}),(0,r.jsx)("p",{className:"text-xl mb-8 max-w-2xl mx-auto",children:"Discover a wide range of rental properties that match your lifestyle and budget"}),(0,r.jsx)(i.SearchBar,{})]})]}),(0,r.jsx)("section",{className:"py-16 bg-gray-100",children:(0,r.jsxs)("div",{className:"container-custom",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold mb-12 text-center",children:"Your Renting Journey"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-accent text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4",children:"1"}),(0,r.jsx)("h3",{className:"text-xl font-bold mb-3",children:"Determine Your Budget"}),(0,r.jsx)("p",{className:"text-text-secondary",children:"Calculate how much rent you can afford, including utilities and other monthly expenses."})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-accent text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4",children:"2"}),(0,r.jsx)("h3",{className:"text-xl font-bold mb-3",children:"Find Your Rental"}),(0,r.jsx)("p",{className:"text-text-secondary",children:"Browse our listings, schedule viewings, and find the perfect rental property that meets your needs."})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-accent text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4",children:"3"}),(0,r.jsx)("h3",{className:"text-xl font-bold mb-3",children:"Apply for the Property"}),(0,r.jsx)("p",{className:"text-text-secondary",children:"Submit your rental application, including references, credit check, and proof of income."})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-accent text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4",children:"4"}),(0,r.jsx)("h3",{className:"text-xl font-bold mb-3",children:"Sign the Lease"}),(0,r.jsx)("p",{className:"text-text-secondary",children:"Review the lease agreement, pay the security deposit, and move into your new home."})]})]}),(0,r.jsx)("div",{className:"text-center mt-10",children:(0,r.jsx)(d(),{href:"/renting-guide",className:"btn-accent",children:"View Complete Renting Guide"})})]})}),(0,r.jsx)("section",{className:"py-16",children:(0,r.jsxs)("div",{className:"container-custom",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold mb-12 text-center",children:"Properties For Rent"}),(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,r.jsx)("div",{className:"lg:w-1/4",children:(0,r.jsx)(o.f,{onFilterChange:g})}),(0,r.jsxs)("div",{className:"lg:w-3/4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsxs)("p",{className:"text-text-secondary",children:["Showing ",(0,r.jsx)("span",{className:"font-semibold",children:"24"})," properties"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("label",{htmlFor:"sort",className:"text-text-secondary",children:"Sort by:"}),(0,r.jsxs)("select",{id:"sort",className:"border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent",children:[(0,r.jsx)("option",{value:"newest",children:"Newest"}),(0,r.jsx)("option",{value:"price-asc",children:"Price (Low to High)"}),(0,r.jsx)("option",{value:"price-desc",children:"Price (High to Low)"}),(0,r.jsx)("option",{value:"popular",children:"Most Popular"})]})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:(0,r.jsxs)("div",{className:"col-span-full text-center py-8",children:[(0,r.jsx)("p",{className:"text-gray-500",children:"Rental properties will be displayed here"}),(0,r.jsx)(d(),{href:"/properties",className:"text-primary hover:underline mt-2 inline-block",children:"Browse All Properties"})]})}),(0,r.jsx)("div",{className:"mt-12 flex justify-center",children:(0,r.jsxs)("nav",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{className:"w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center text-gray-500 hover:bg-gray-100",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),(0,r.jsx)("button",{className:"w-10 h-10 rounded-md bg-accent text-white flex items-center justify-center",children:"1"}),(0,r.jsx)("button",{className:"w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center hover:bg-gray-100",children:"2"}),(0,r.jsx)("button",{className:"w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center hover:bg-gray-100",children:"3"}),(0,r.jsx)("span",{className:"text-gray-500",children:"..."}),(0,r.jsx)("button",{className:"w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center hover:bg-gray-100",children:"10"}),(0,r.jsx)("button",{className:"w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center text-gray-500 hover:bg-gray-100",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})})]})]})]})}),(0,r.jsx)("section",{className:"py-16 bg-gray-100",children:(0,r.jsxs)("div",{className:"container-custom",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold mb-12 text-center",children:"Renting Tips"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,r.jsx)("div",{className:"relative h-48",children:(0,r.jsx)(h.default,{src:"https://images.unsplash.com/photo-1560520031-3a4dc4e9de0c?q=80&w=1473&auto=format&fit=crop",alt:"Budgeting for Rent",fill:!0,className:"object-cover"})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-xl font-bold mb-3",children:"Budgeting for Rent"}),(0,r.jsx)("p",{className:"text-text-secondary mb-4",children:"Learn how to budget for your rental, including security deposits, monthly rent, utilities, and other expenses."}),(0,r.jsx)(d(),{href:"/blog/budgeting-for-rent",className:"text-accent font-semibold hover:underline",children:"Read More"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,r.jsx)("div",{className:"relative h-48",children:(0,r.jsx)(h.default,{src:"https://images.unsplash.com/photo-1628744448840-55bdb2497bd4?q=80&w=1470&auto=format&fit=crop",alt:"Rental Inspection",fill:!0,className:"object-cover"})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-xl font-bold mb-3",children:"Rental Inspection Checklist"}),(0,r.jsx)("p",{className:"text-text-secondary mb-4",children:"What to look for during a rental property inspection and red flags that could save you from a bad rental experience."}),(0,r.jsx)(d(),{href:"/blog/rental-inspection",className:"text-accent font-semibold hover:underline",children:"Read More"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,r.jsx)("div",{className:"relative h-48",children:(0,r.jsx)(h.default,{src:"https://images.unsplash.com/photo-1450101499163-c8848c66ca85?q=80&w=1470&auto=format&fit=crop",alt:"Lease Agreement Tips",fill:!0,className:"object-cover"})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-xl font-bold mb-3",children:"Understanding Lease Agreements"}),(0,r.jsx)("p",{className:"text-text-secondary mb-4",children:"Expert tips on understanding your lease agreement, including terms, conditions, and your rights as a tenant."}),(0,r.jsx)(d(),{href:"/blog/lease-agreements",className:"text-accent font-semibold hover:underline",children:"Read More"})]})]})]})]})}),(0,r.jsx)("section",{className:"py-16",children:(0,r.jsxs)("div",{className:"container-custom",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold mb-12 text-center",children:"Popular Rental Neighborhoods"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"relative rounded-lg overflow-hidden group h-80",children:[(0,r.jsx)(h.default,{src:"https://images.unsplash.com/photo-1519501025264-65ba15a82390?q=80&w=1464&auto=format&fit=crop",alt:"Downtown",fill:!0,className:"object-cover transition-transform duration-500 group-hover:scale-110"}),(0,r.jsxs)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex flex-col justify-end p-6 text-white",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold mb-2",children:"Downtown"}),(0,r.jsx)("p",{className:"mb-3",children:"Urban living with easy access to restaurants, shops, and entertainment"}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"font-semibold mr-4",children:"Avg. Rent: $2,200/mo"}),(0,r.jsx)(d(),{href:"/neighborhoods/downtown",className:"text-white underline hover:text-accent",children:"View Properties"})]})]})]}),(0,r.jsxs)("div",{className:"relative rounded-lg overflow-hidden group h-80",children:[(0,r.jsx)(h.default,{src:"https://images.unsplash.com/photo-1600047509807-ba8f99d2cdde?q=80&w=1384&auto=format&fit=crop",alt:"Riverside",fill:!0,className:"object-cover transition-transform duration-500 group-hover:scale-110"}),(0,r.jsxs)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex flex-col justify-end p-6 text-white",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold mb-2",children:"Riverside"}),(0,r.jsx)("p",{className:"mb-3",children:"Scenic views with parks, trails, and a relaxed atmosphere"}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"font-semibold mr-4",children:"Avg. Rent: $1,800/mo"}),(0,r.jsx)(d(),{href:"/neighborhoods/riverside",className:"text-white underline hover:text-accent",children:"View Properties"})]})]})]}),(0,r.jsxs)("div",{className:"relative rounded-lg overflow-hidden group h-80",children:[(0,r.jsx)(h.default,{src:"https://images.unsplash.com/photo-1604014237800-1c9102c219da?q=80&w=1470&auto=format&fit=crop",alt:"University District",fill:!0,className:"object-cover transition-transform duration-500 group-hover:scale-110"}),(0,r.jsxs)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex flex-col justify-end p-6 text-white",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold mb-2",children:"University District"}),(0,r.jsx)("p",{className:"mb-3",children:"Vibrant area with student life, cafes, and affordable housing"}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"font-semibold mr-4",children:"Avg. Rent: $1,500/mo"}),(0,r.jsx)(d(),{href:"/neighborhoods/university-district",className:"text-white underline hover:text-accent",children:"View Properties"})]})]})]})]})]})}),(0,r.jsx)("section",{className:"py-16 bg-accent text-white",children:(0,r.jsxs)("div",{className:"container-custom text-center",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold mb-6",children:"Ready to Find Your Perfect Rental?"}),(0,r.jsx)("p",{className:"text-xl mb-8 max-w-2xl mx-auto",children:"Our expert agents are ready to help you find the ideal rental property that fits your lifestyle and budget."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row justify-center gap-4",children:[(0,r.jsx)(d(),{href:"/contact",className:"btn-white",children:"Contact an Agent"}),(0,r.jsx)(d(),{href:"/properties",className:"btn-outline-white",children:"Browse All Rentals"})]})]})}),(0,r.jsx)(l.w,{})]})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1261:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return o},getImageProps:function(){return i}});let r=s(4985),a=s(4953),n=s(6533),l=r._(s(1933));function i(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let o=n.Image},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5504:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>h,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=s(5239),a=s(8088),n=s(8170),l=s.n(n),i=s(893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let c={children:["",{children:["rent",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,6120)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\rent\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\rent\\page.tsx"],h={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/rent/page",pathname:"/rent",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},6120:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Music\\\\Real Estate\\\\Real Estate\\\\src\\\\app\\\\rent\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Music\\Real Estate\\Real Estate\\src\\app\\rent\\page.tsx","default")},8063:(e,t,s)=>{Promise.resolve().then(s.bind(s,6120))},8311:(e,t,s)=>{Promise.resolve().then(s.bind(s,730))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[771,814,533,436,317,540,870],()=>s(5504));module.exports=r})();